// Complete integration test for all functionality
require('dotenv').config();
const { connectDB } = require('./config/database');

async function testCompleteIntegration() {
  try {
    console.log('🧪 Starting complete integration test...');
    
    // Connect to database
    await connectDB();
    console.log('✅ Database connected');
    
    const { User, Category, Product, Order, Payment } = require('./models');
    
    // Test 1: Database counts
    console.log('\n📊 Database Status:');
    const userCount = await User.count();
    const categoryCount = await Category.count();
    const productCount = await Product.count();
    const orderCount = await Order.count();
    const paymentCount = await Payment.count();
    
    console.log(`- Users: ${userCount}`);
    console.log(`- Categories: ${categoryCount}`);
    console.log(`- Products: ${productCount}`);
    console.log(`- Orders: ${orderCount}`);
    console.log(`- Payments: ${paymentCount}`);
    
    // Test 2: Product counts by category
    console.log('\n📦 Product counts by category:');
    const categories = await Category.findAll({ where: { parentId: null } });
    
    for (const category of categories) {
      const count = await Product.count({ where: { categoryId: category.id } });
      console.log(`- ${category.name}: ${count} products`);
      
      // Show subcategory breakdown
      const subcategories = await Category.findAll({ where: { parentId: category.id } });
      for (const subcat of subcategories) {
        const subcatCount = await Product.count({ where: { subcategoryId: subcat.id } });
        console.log(`  - ${subcat.name}: ${subcatCount} products`);
      }
    }
    
    // Test 3: Sample products from each category
    console.log('\n🛍️ Sample products:');
    const sampleProducts = await Product.findAll({
      limit: 5,
      include: [
        { model: Category, as: 'category', attributes: ['name'] },
        { model: Category, as: 'subcategory', attributes: ['name'] }
      ]
    });
    
    sampleProducts.forEach(product => {
      console.log(`- ${product.name} (${product.category?.name} > ${product.subcategory?.name}) - $${product.salePrice}`);
    });
    
    // Test 4: API endpoints simulation
    console.log('\n🔗 Testing API functionality:');
    
    // Test product filtering
    const womenTshirts = await Product.findAll({
      include: [
        {
          model: Category,
          as: 'subcategory',
          where: { slug: 'women-t-shirt' }
        }
      ],
      limit: 3
    });
    console.log(`✅ Women's T-shirts query: ${womenTshirts.length} results`);
    
    // Test search functionality
    const searchResults = await Product.findAll({
      where: {
        name: { [require('sequelize').Op.like]: '%shirt%' }
      },
      limit: 5
    });
    console.log(`✅ Search for 'shirt': ${searchResults.length} results`);
    
    // Test 5: User authentication simulation
    console.log('\n👤 User authentication test:');
    const adminUser = await User.findOne({ where: { email: '<EMAIL>' } });
    if (adminUser) {
      console.log(`✅ Admin user found: ${adminUser.name} (${adminUser.email})`);
    } else {
      console.log('❌ Admin user not found');
    }
    
    // Test 6: Order and payment tables
    console.log('\n💳 Order and payment system:');
    console.log(`✅ Orders table ready: ${orderCount} existing orders`);
    console.log(`✅ Payments table ready: ${paymentCount} existing payments`);
    
    // Test 7: Expected product counts verification
    console.log('\n✅ Product count verification:');
    const expectedCounts = {
      'Women T-shirt': 31,
      'Women Shirt': 43,
      'Women Jeans': 16,
      'Women Trouser': 17,
      'Women Jacket': 20,
      'Women Skirt': 17,
      'Women Shorts': 19,
      'Women Dress': 23,
      'Women Shoes': 20,
      'Men T-shirt': 24,
      'Men Shirt': 22,
      'Men Jeans': 10,
      'Men Trouser': 9,
      'Men Jacket': 19,
      'Men Shoes': 17,
      'Boys Clothing': 45,
      'Boys Shoes': 17,
      'Girls Clothing': 46,
      'Girls Shoes': 20
    };
    
    let allCorrect = true;
    for (const [subcatName, expectedCount] of Object.entries(expectedCounts)) {
      const subcat = await Category.findOne({ where: { name: subcatName } });
      if (subcat) {
        const actualCount = await Product.count({ where: { subcategoryId: subcat.id } });
        const isCorrect = actualCount === expectedCount;
        if (!isCorrect) allCorrect = false;
        console.log(`${subcatName}: ${actualCount}/${expectedCount} ${isCorrect ? '✅' : '❌'}`);
      }
    }
    
    // Accessories verification
    const accessoryCategories = ['Glasses', 'Watches', 'Gloves', 'Belt', 'Hat', 'Bag', 'Wallet'];
    const accessoryExpected = [19, 6, 5, 9, 16, 33, 8];
    
    for (let i = 0; i < accessoryCategories.length; i++) {
      const subcat = await Category.findOne({ where: { slug: accessoryCategories[i].toLowerCase() } });
      if (subcat) {
        const actualCount = await Product.count({ where: { subcategoryId: subcat.id } });
        const isCorrect = actualCount === accessoryExpected[i];
        if (!isCorrect) allCorrect = false;
        console.log(`${accessoryCategories[i]}: ${actualCount}/${accessoryExpected[i]} ${isCorrect ? '✅' : '❌'}`);
      }
    }
    
    console.log(`\n🎉 Integration test completed!`);
    console.log(`📊 Overall status: ${allCorrect ? '✅ All systems ready!' : '⚠️ Some issues found'}`);
    console.log(`\n🚀 Ready for frontend integration:`);
    console.log(`- Backend server: http://localhost:3001`);
    console.log(`- Frontend server: http://localhost:5174`);
    console.log(`- API test page: http://localhost:5174/api-test`);
    console.log(`- Total products available: ${productCount}`);
    
  } catch (error) {
    console.error('❌ Integration test failed:', error.message);
    console.error(error.stack);
  } finally {
    process.exit(0);
  }
}

testCompleteIntegration();
