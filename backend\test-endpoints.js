// Test backend endpoints functionality
require('dotenv').config();
const { connectDB } = require('./config/database');
const { Product, Category, User, Order, OrderItem, Payment } = require('./models');
const bcrypt = require('bcryptjs');

async function testEndpoints() {
  try {
    console.log('🧪 Testing Backend Endpoints...\n');
    
    // Connect to database
    await connectDB();
    console.log('✅ Database connected\n');
    
    // Test 1: Product functionality
    console.log('1. Testing Product Functionality:');
    const products = await Product.findAll({
      limit: 3,
      include: [{ model: Category, as: 'category' }]
    });
    
    console.log(`   ✅ Found ${products.length} products`);
    if (products.length > 0) {
      const product = products[0];
      console.log(`   ✅ Sample product: ${product.name}`);
      console.log(`   ✅ Price: $${product.originalPrice} -> $${product.salePrice} (${product.discount}% off)`);
      console.log(`   ✅ Colors: ${JSON.parse(product.colors || '[]').join(', ')}`);
      console.log(`   ✅ Sizes: ${JSON.parse(product.sizes || '[]').join(', ')}`);
    }
    
    // Test 2: Product search
    console.log('\n2. Testing Product Search:');
    const searchResults = await Product.findAll({
      where: {
        name: {
          [require('sequelize').Op.like]: '%T-Shirt%'
        }
      },
      limit: 2
    });
    console.log(`   ✅ Search for "T-Shirt" found ${searchResults.length} results`);
    
    // Test 3: Categories
    console.log('\n3. Testing Categories:');
    const categories = await Category.findAll({
      where: { parentId: null },
      limit: 3
    });
    console.log(`   ✅ Found ${categories.length} main categories`);
    categories.forEach(cat => {
      console.log(`   ✅ Category: ${cat.name} (${cat.slug})`);
    });
    
    // Test 4: User functionality
    console.log('\n4. Testing User Functionality:');
    const userCount = await User.count();
    console.log(`   ✅ Found ${userCount} users in database`);
    
    // Test creating a test user
    const testUserEmail = '<EMAIL>';
    let testUser = await User.findOne({ where: { email: testUserEmail } });
    
    if (!testUser) {
      const hashedPassword = await bcrypt.hash('password123', 10);
      testUser = await User.create({
        name: 'Test User',
        email: testUserEmail,
        password: hashedPassword,
        role: 'user'
      });
      console.log('   ✅ Created test user');
    } else {
      console.log('   ✅ Test user already exists');
    }
    
    // Test 5: Order functionality
    console.log('\n5. Testing Order Functionality:');
    const orderCount = await Order.count();
    console.log(`   ✅ Found ${orderCount} orders in database`);
    
    // Test 6: Payment functionality
    console.log('\n6. Testing Payment Functionality:');
    const paymentCount = await Payment.count();
    console.log(`   ✅ Found ${paymentCount} payments in database`);
    
    // Test 7: Model associations
    console.log('\n7. Testing Model Associations:');
    const productWithCategory = await Product.findOne({
      include: [{ model: Category, as: 'category' }]
    });
    
    if (productWithCategory && productWithCategory.category) {
      console.log(`   ✅ Product-Category association working`);
      console.log(`   ✅ ${productWithCategory.name} belongs to ${productWithCategory.category.name}`);
    }
    
    console.log('\n🎉 All endpoint tests completed successfully!');
    console.log('\n📋 Summary:');
    console.log(`   - Products: ${products.length} available`);
    console.log(`   - Categories: ${categories.length} main categories`);
    console.log(`   - Users: ${userCount} registered`);
    console.log(`   - Orders: ${orderCount} total`);
    console.log(`   - Payments: ${paymentCount} processed`);
    
    console.log('\n✅ Backend is ready for frontend integration!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
  } finally {
    process.exit(0);
  }
}

testEndpoints();
