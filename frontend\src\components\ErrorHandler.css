/* <PERSON><PERSON><PERSON> Styles */
/* File: frontend/src/components/ErrorHandler.css */

.error-handler {
  position: fixed;
  top: 20px;
  right: 20px;
  max-width: 400px;
  min-width: 300px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  z-index: 9999;
  animation: slideInRight 0.3s ease-out;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.error-critical {
  border-left: 5px solid #dc3545;
}

.error-warning {
  border-left: 5px solid #ffc107;
}

.error-auth {
  border-left: 5px solid #6f42c1;
}

.error-validation {
  border-left: 5px solid #fd7e14;
}

.error-default {
  border-left: 5px solid #6c757d;
}

.error-content {
  padding: 16px;
}

.error-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.error-icon {
  font-size: 20px;
  margin-right: 8px;
}

.error-title {
  flex: 1;
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #212529;
}

.error-close {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #6c757d;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.error-close:hover {
  background-color: #f8f9fa;
  color: #495057;
}

.error-body {
  margin-bottom: 16px;
}

.error-message {
  margin: 0 0 8px 0;
  color: #495057;
  font-size: 14px;
  line-height: 1.4;
}

.error-details {
  margin: 8px 0;
}

.error-details summary {
  cursor: pointer;
  font-size: 13px;
  color: #6c757d;
  padding: 4px 0;
  outline: none;
}

.error-details summary:hover {
  color: #495057;
}

.error-details p {
  margin: 8px 0 0 16px;
  font-size: 12px;
  color: #6c757d;
  font-family: 'Courier New', monospace;
  background: #f8f9fa;
  padding: 8px;
  border-radius: 4px;
  word-break: break-word;
}

.error-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.btn-retry,
.btn-dismiss {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  font-weight: 500;
}

.btn-retry {
  background-color: #007bff;
  color: white;
}

.btn-retry:hover:not(:disabled) {
  background-color: #0056b3;
}

.btn-retry:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
  opacity: 0.6;
}

.btn-dismiss {
  background-color: #f8f9fa;
  color: #495057;
  border: 1px solid #dee2e6;
}

.btn-dismiss:hover {
  background-color: #e9ecef;
  border-color: #adb5bd;
}

.connection-help {
  margin: 12px 0;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #dee2e6;
}

.connection-help h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #495057;
}

.connection-help ol {
  margin: 8px 0;
  padding-left: 20px;
  font-size: 13px;
  color: #6c757d;
}

.connection-help ol li {
  margin: 4px 0;
  line-height: 1.4;
}

.connection-help p {
  margin: 8px 0 0 0;
  font-size: 12px;
  color: #495057;
}

.connection-help code {
  background-color: #e9ecef;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 11px;
}

/* Connection Status Indicator */
.connection-status {
  position: fixed;
  bottom: 20px;
  right: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
  background: white;
  padding: 8px 12px;
  border-radius: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  font-size: 12px;
  z-index: 1000;
  transition: all 0.3s ease;
}

.connection-status.connected {
  border: 1px solid #28a745;
  color: #155724;
}

.connection-status.disconnected {
  border: 1px solid #dc3545;
  color: #721c24;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.status-indicator {
  font-size: 10px;
}

.status-text {
  font-weight: 500;
}

.last-check {
  color: #6c757d;
  font-size: 10px;
}

/* Mobile Responsiveness */
@media (max-width: 480px) {
  .error-handler {
    top: 10px;
    right: 10px;
    left: 10px;
    max-width: none;
    min-width: auto;
  }
  
  .connection-status {
    bottom: 10px;
    right: 10px;
    font-size: 11px;
  }
  
  .error-content {
    padding: 12px;
  }
  
  .error-title {
    font-size: 14px;
  }
  
  .error-message {
    font-size: 13px;
  }
}
