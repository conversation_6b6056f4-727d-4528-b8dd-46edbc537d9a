/* 🎨 Enhanced Collection Styles - Screenshot Layout */
/* File: frontend/src/styles/CollectionDatabase.css */

.collection-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
  font-family: 'Arial', sans-serif;
}

.collection-header {
  text-align: center;
  margin-bottom: 40px;
}

.collection-header h1 {
  font-size: 2.5rem;
  color: #2c3e50;
  margin-bottom: 10px;
  font-weight: 700;
}

.collection-header p {
  font-size: 1.1rem;
  color: #7f8c8d;
  margin: 0;
}

/* ================================ */
/* 📂 CATEGORY SECTIONS */
/* ================================ */
.category-section {
  margin-bottom: 60px;
}

.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 15px;
  border-bottom: 3px solid #e74c3c;
}

.category-title {
  font-size: 2rem;
  color: #2c3e50;
  margin: 0;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.product-count {
  background: #3498db;
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
}

/* ================================ */
/* 🛍️ PRODUCTS GRID - LIKE SCREENSHOT */
/* ================================ */
.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 25px;
  padding: 20px 0;
}

/* ================================ */
/* 🏷️ PRODUCT CARDS - SCREENSHOT STYLE */
/* ================================ */
.product-card {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
}

.product-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.product-card.out-of-stock {
  opacity: 0.6;
  cursor: not-allowed;
}

/* ================================ */
/* 🖼️ IMAGE CONTAINER */
/* ================================ */
.product-image-container {
  position: relative;
  width: 100%;
  height: 320px;
  overflow: hidden;
  background: #f8f9fa;
}

.product-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.product-card:hover .product-image {
  transform: scale(1.05);
}

/* ================================ */
/* 🏷️ SALE BADGE - EXACTLY LIKE SCREENSHOT */
/* ================================ */
.sale-badge {
  position: absolute;
  top: 15px;
  left: 15px;
  background: #e74c3c;
  color: white;
  padding: 8px 16px;
  font-size: 0.8rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 1px;
  border-radius: 25px;
  z-index: 10;
  box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
}

/* ================================ */
/* 📦 STOCK BADGES */
/* ================================ */
.stock-badge {
  position: absolute;
  top: 15px;
  right: 15px;
  padding: 6px 12px;
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
  border-radius: 15px;
  z-index: 10;
}

.out-of-stock-badge {
  background: #95a5a6;
  color: white;
}

.low-stock-badge {
  background: #f39c12;
  color: white;
}

/* ================================ */
/* 👀 QUICK VIEW OVERLAY */
/* ================================ */
.product-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.product-card:hover .product-overlay {
  opacity: 1;
}

.quick-view-btn {
  background: white;
  color: #2c3e50;
  border: none;
  padding: 12px 24px;
  border-radius: 25px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.quick-view-btn:hover {
  background: #3498db;
  color: white;
  transform: scale(1.05);
}

/* ================================ */
/* 📋 PRODUCT INFO SECTION */
/* ================================ */
.product-info {
  padding: 20px;
  background: white;
}

.product-name {
  font-size: 1.1rem;
  color: #2c3e50;
  margin: 0 0 10px 0;
  font-weight: 600;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* ================================ */
/* 💰 PRICE CONTAINER - LIKE SCREENSHOT */
/* ================================ */
.price-container {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}

.current-price {
  font-size: 1.3rem;
  font-weight: 700;
  color: #e74c3c;
}

.original-price {
  font-size: 1rem;
  color: #95a5a6;
  text-decoration: line-through;
  font-weight: 500;
}

/* ================================ */
/* 🎨 COLOR & SIZE OPTIONS */
/* ================================ */
.product-details {
  margin-bottom: 15px;
}

.color-options {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.color-dot {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  cursor: pointer;
  transition: transform 0.2s ease;
  border: 2px solid transparent;
}

.color-dot:hover {
  transform: scale(1.2);
  border-color: #3498db;
}

.more-colors {
  font-size: 0.8rem;
  color: #7f8c8d;
  font-weight: 500;
}

.size-info {
  margin-top: 8px;
}

.sizes-available {
  font-size: 0.8rem;
  color: #7f8c8d;
  font-weight: 500;
}

/* ================================ */
/* 📦 STOCK STATUS */
/* ================================ */
.stock-status {
  display: flex;
  align-items: center;
  font-size: 0.8rem;
  font-weight: 500;
}

.in-stock {
  color: #27ae60;
}

.low-stock {
  color: #f39c12;
}

.out-of-stock {
  color: #e74c3c;
}

/* ================================ */
/* 🔄 LOADING & ERROR STATES */
/* ================================ */
.collection-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  flex-direction: column;
}

.loading-container {
  text-align: center;
}

.loading-spinner {
  margin-bottom: 20px;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #ecf0f1;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.collection-error {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.error-container {
  text-align: center;
  max-width: 500px;
}

.error-icon {
  font-size: 3rem;
  margin-bottom: 20px;
}

.retry-button {
  background: #3498db;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  margin-top: 20px;
  transition: background 0.3s ease;
}

.retry-button:hover {
  background: #2980b9;
}

/* ================================ */
/* 📊 DATABASE STATUS */
/* ================================ */
.database-status {
  margin-top: 40px;
  padding: 20px;
  background: #ecf0f1;
  border-radius: 12px;
  text-align: center;
}

.status-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 8px;
  font-weight: 600;
  color: #2c3e50;
}

.status-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: #27ae60;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(39, 174, 96, 0.7);
  }
  70% {
    transform: scale(1);
    box-shadow: 0 0 0 10px rgba(39, 174, 96, 0);
  }
  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(39, 174, 96, 0);
  }
}

.database-status small {
  color: #7f8c8d;
  font-size: 0.9rem;
}

/* ================================ */
/* 📱 RESPONSIVE DESIGN */
/* ================================ */
@media (max-width: 768px) {
  .products-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
  }
  
  .category-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
  
  .collection-header h1 {
    font-size: 2rem;
  }
  
  .category-title {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .products-grid {
    grid-template-columns: 1fr;
  }
  
  .collection-container {
    padding: 15px;
  }
  
  .product-image-container {
    height: 280px;
  }
}
