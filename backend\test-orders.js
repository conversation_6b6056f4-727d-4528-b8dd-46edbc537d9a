const axios = require('axios');

const testOrders = async () => {
  try {
    // Register a new user to get token
    const registerResponse = await axios.post('http://localhost:3001/api/auth/register', {
      name: 'Order Test User',
      email: `test_orders_${Date.now()}@example.com`,
      password: 'password123'
    });

    const token = registerResponse.data.token;
    console.log('✅ Registration successful');

    // Get user order history
    const ordersResponse = await axios.get('http://localhost:3001/api/orders/history', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    console.log('✅ Order history retrieved:');
    console.log(JSON.stringify(ordersResponse.data, null, 2));
  } catch (error) {
    console.error('❌ Error:', error.response?.data || error.message);
  }
};

testOrders();
