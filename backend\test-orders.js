const axios = require('axios');

const testOrders = async () => {
  try {
    // Login to get token
    const loginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      email: '<EMAIL>',
      password: 'password123'
    });

    const token = loginResponse.data.token;
    console.log('✅ Login successful');

    // Get order history
    const ordersResponse = await axios.get('http://localhost:3001/api/orders', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    console.log('✅ Order history retrieved:');
    console.log(JSON.stringify(ordersResponse.data, null, 2));
  } catch (error) {
    console.error('❌ Error:', error.response?.data || error.message);
  }
};

testOrders();
