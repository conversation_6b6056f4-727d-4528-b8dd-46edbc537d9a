const axios = require('axios');

const testUserProfile = async () => {
  try {
    console.log('🧪 Testing user profile endpoint...\n');

    // Register a new user
    const email = `test_profile_${Date.now()}@example.com`;
    const registerResponse = await axios.post('http://localhost:3001/api/auth/register', {
      name: 'Profile Test User',
      email: email,
      password: 'password123'
    });

    const token = registerResponse.data.token;
    console.log('✅ User registered successfully');

    // Test user profile endpoint
    const profileResponse = await axios.get('http://localhost:3001/api/user/profile', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    console.log('✅ User profile retrieved successfully');
    console.log('Profile data:', JSON.stringify(profileResponse.data, null, 2));

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
};

testUserProfile();
