const { Order, OrderItem, Payment, Product, User } = require('../models');
const { sequelize } = require('../config/database');
const { validationResult } = require('express-validator');

// Process payment after user clicks "Buy"
const processPayment = async (req, res) => {
  // Start database transaction for data consistency
  const transaction = await sequelize.transaction();
  
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      await transaction.rollback();
      return res.status(400).json({ 
        success: false,
        errors: errors.array() 
      });
    }

    const userId = req.user.id; // From JWT middleware
    const { 
      orderId, 
      paymentMethod, 
      amount,
      paymentGateway = 'manual',
      transactionId = null,
      gatewayResponse = null 
    } = req.body;

    // Step 1: Validate order exists and belongs to user
    const order = await Order.findOne({
      where: { 
        id: orderId, 
        userId: userId,
        orderStatus: 'pending' // Only allow payment for pending orders
      },
      include: [
        {
          model: OrderItem,
          as: 'items',
          include: [{ model: Product }]
        }
      ],
      transaction
    });

    if (!order) {
      await transaction.rollback();
      return res.status(404).json({
        success: false,
        message: 'Order not found or already processed'
      });
    }

    // Step 2: Validate payment amount matches order total
    if (parseFloat(amount) !== parseFloat(order.total)) {
      await transaction.rollback();
      return res.status(400).json({
        success: false,
        message: `Payment amount (${amount}) does not match order total (${order.total})`
      });
    }

    // Step 3: Check product stock availability
    for (const item of order.items) {
      const product = item.Product;
      if (product.stock < item.quantity) {
        await transaction.rollback();
        return res.status(400).json({
          success: false,
          message: `Insufficient stock for ${product.name}. Available: ${product.stock}, Required: ${item.quantity}`
        });
      }
    }

    // Step 4: Create payment record
    const payment = await Payment.create({
      orderId: orderId,
      paymentMethod: paymentMethod,
      amount: amount,
      currency: 'USD',
      status: 'completed', // Assuming payment is successful
      transactionId: transactionId,
      paymentGateway: paymentGateway,
      gatewayResponse: gatewayResponse,
      paidAt: new Date()
    }, { transaction });

    // Step 5: Update order status to "paid"
    await order.update({
      orderStatus: 'confirmed',
      paymentStatus: 'paid'
    }, { transaction });

    // Step 6: Deduct product quantities from stock
    const stockUpdates = [];
    for (const item of order.items) {
      const product = item.Product;
      const newStock = product.stock - item.quantity;
      
      await product.update({
        stock: newStock
      }, { transaction });

      stockUpdates.push({
        productId: product.id,
        productName: product.name,
        previousStock: product.stock,
        soldQuantity: item.quantity,
        newStock: newStock
      });
    }

    // Step 7: Create inventory log (optional but recommended)
    for (const item of order.items) {
      // You could create an InventoryLog model to track stock changes
      console.log(`Stock updated: Product ${item.Product.name} - Sold: ${item.quantity}, Remaining: ${item.Product.stock - item.quantity}`);
    }

    // Commit transaction
    await transaction.commit();

    // Step 8: Return success response
    res.status(200).json({
      success: true,
      message: 'Payment processed successfully',
      data: {
        payment: {
          id: payment.id,
          transactionId: payment.transactionId,
          amount: payment.amount,
          paymentMethod: payment.paymentMethod,
          status: payment.status,
          paidAt: payment.paidAt
        },
        order: {
          id: order.id,
          orderNumber: order.orderNumber,
          status: order.orderStatus,
          paymentStatus: order.paymentStatus,
          total: order.total
        },
        stockUpdates: stockUpdates,
        items: order.items.map(item => ({
          productName: item.Product.name,
          quantity: item.quantity,
          price: item.price,
          color: item.color,
          size: item.size
        }))
      }
    });

  } catch (error) {
    // Rollback transaction on error
    await transaction.rollback();
    console.error('Payment processing error:', error);
    
    res.status(500).json({
      success: false,
      message: 'Payment processing failed',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
};

// Get payment details
const getPaymentDetails = async (req, res) => {
  try {
    const { paymentId } = req.params;
    const userId = req.user.id;

    const payment = await Payment.findOne({
      where: { id: paymentId },
      include: [
        {
          model: Order,
          where: { userId: userId }, // Ensure user owns this payment
          include: [
            {
              model: OrderItem,
              as: 'items',
              include: [{ model: Product, attributes: ['id', 'name', 'images'] }]
            }
          ]
        }
      ]
    });

    if (!payment) {
      return res.status(404).json({
        success: false,
        message: 'Payment not found'
      });
    }

    res.json({
      success: true,
      payment: payment
    });

  } catch (error) {
    console.error('Get payment details error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// Get user's payment history
const getUserPayments = async (req, res) => {
  try {
    const userId = req.user.id;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;

    const payments = await Payment.findAndCountAll({
      include: [
        {
          model: Order,
          where: { userId: userId },
          include: [
            {
              model: OrderItem,
              as: 'items',
              include: [{ model: Product, attributes: ['id', 'name', 'images'] }]
            }
          ]
        }
      ],
      order: [['createdAt', 'DESC']],
      limit: limit,
      offset: offset
    });

    res.json({
      success: true,
      payments: payments.rows,
      pagination: {
        page: page,
        limit: limit,
        total: payments.count,
        totalPages: Math.ceil(payments.count / limit)
      }
    });

  } catch (error) {
    console.error('Get user payments error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// Refund payment (admin only)
const refundPayment = async (req, res) => {
  const transaction = await sequelize.transaction();
  
  try {
    const { paymentId } = req.params;
    const { refundAmount, reason } = req.body;

    const payment = await Payment.findByPk(paymentId, {
      include: [
        {
          model: Order,
          include: [
            {
              model: OrderItem,
              as: 'items',
              include: [{ model: Product }]
            }
          ]
        }
      ],
      transaction
    });

    if (!payment) {
      await transaction.rollback();
      return res.status(404).json({
        success: false,
        message: 'Payment not found'
      });
    }

    if (payment.status !== 'completed') {
      await transaction.rollback();
      return res.status(400).json({
        success: false,
        message: 'Can only refund completed payments'
      });
    }

    const refundAmountNum = parseFloat(refundAmount);
    if (refundAmountNum > parseFloat(payment.amount)) {
      await transaction.rollback();
      return res.status(400).json({
        success: false,
        message: 'Refund amount cannot exceed payment amount'
      });
    }

    // Update payment
    await payment.update({
      status: 'refunded',
      refundAmount: refundAmountNum,
      refundedAt: new Date(),
      failureReason: reason
    }, { transaction });

    // Update order status
    await payment.Order.update({
      orderStatus: 'cancelled',
      paymentStatus: 'refunded'
    }, { transaction });

    // Restore product stock
    for (const item of payment.Order.items) {
      const product = item.Product;
      await product.update({
        stock: product.stock + item.quantity
      }, { transaction });
    }

    await transaction.commit();

    res.json({
      success: true,
      message: 'Payment refunded successfully',
      refund: {
        paymentId: payment.id,
        refundAmount: refundAmountNum,
        refundedAt: payment.refundedAt
      }
    });

  } catch (error) {
    await transaction.rollback();
    console.error('Refund payment error:', error);
    res.status(500).json({
      success: false,
      message: 'Refund processing failed'
    });
  }
};

// Complete purchase workflow (create order + process payment)
const completePurchase = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      await transaction.rollback();
      return res.status(400).json({
        success: false,
        errors: errors.array()
      });
    }

    const userId = req.user.id;
    const {
      items,
      shippingAddress,
      billingAddress,
      paymentMethod,
      paymentGateway = 'manual',
      transactionId = null
    } = req.body;

    // Step 1: Validate products and calculate total
    let subtotal = 0;
    const orderItems = [];

    for (const item of items) {
      const product = await Product.findByPk(item.productId, { transaction });
      if (!product) {
        await transaction.rollback();
        return res.status(400).json({
          success: false,
          message: `Product not found: ${item.productId}`
        });
      }

      if (product.stock < item.quantity) {
        await transaction.rollback();
        return res.status(400).json({
          success: false,
          message: `Insufficient stock for ${product.name}. Available: ${product.stock}`
        });
      }

      const itemTotal = product.salePrice * item.quantity;
      subtotal += itemTotal;

      orderItems.push({
        productId: product.id,
        quantity: item.quantity,
        price: product.salePrice,
        color: item.color,
        size: item.size
      });

      // Update product stock
      await product.update({
        stock: product.stock - item.quantity
      }, { transaction });
    }

    // Step 2: Calculate totals
    const shippingCost = subtotal > 50 ? 0 : 10;
    const tax = subtotal * 0.1;
    const total = subtotal + shippingCost + tax;

    // Step 3: Create order
    const orderNumber = `ORD-${Date.now()}-${Math.floor(Math.random() * 1000)}`;

    const order = await Order.create({
      userId,
      orderNumber,
      shippingAddress,
      billingAddress: billingAddress || shippingAddress,
      paymentMethod,
      subtotal,
      shippingCost,
      tax,
      total,
      orderStatus: 'confirmed',
      paymentStatus: 'paid'
    }, { transaction });

    // Step 4: Create order items
    for (const item of orderItems) {
      await OrderItem.create({
        orderId: order.id,
        ...item
      }, { transaction });
    }

    // Step 5: Create payment record
    const payment = await Payment.create({
      orderId: order.id,
      paymentMethod,
      amount: total,
      currency: 'USD',
      status: 'completed',
      transactionId: transactionId || `TXN-${Date.now()}-${Math.floor(Math.random() * 10000)}`,
      paymentGateway,
      paidAt: new Date()
    }, { transaction });

    await transaction.commit();

    // Step 6: Fetch complete order with all details
    const completeOrder = await Order.findByPk(order.id, {
      include: [
        {
          model: OrderItem,
          as: 'items',
          include: [{ model: Product }]
        },
        {
          model: User,
          attributes: ['id', 'name', 'email']
        }
      ]
    });

    // Generate receipt data
    const receipt = {
      receiptNumber: `RCP-${order.orderNumber}`,
      orderNumber: order.orderNumber,
      orderDate: order.createdAt,
      paymentDate: payment.paidAt,

      customer: {
        name: completeOrder.User.name,
        email: completeOrder.User.email
      },

      shippingAddress: order.shippingAddress,
      billingAddress: order.billingAddress,

      items: completeOrder.items.map(item => ({
        productId: item.Product.id,
        productName: item.Product.name,
        brand: item.Product.brand,
        quantity: item.quantity,
        unitPrice: parseFloat(item.price),
        color: item.color,
        size: item.size,
        total: parseFloat(item.price) * item.quantity
      })),

      payment: {
        method: order.paymentMethod,
        transactionId: payment.transactionId,
        status: payment.status
      },

      totals: {
        subtotal: parseFloat(order.subtotal),
        shippingCost: parseFloat(order.shippingCost),
        tax: parseFloat(order.tax),
        total: parseFloat(order.total)
      },

      generatedAt: new Date()
    };

    res.status(201).json({
      success: true,
      message: 'Purchase completed successfully',
      order: completeOrder,
      payment: {
        id: payment.id,
        transactionId: payment.transactionId,
        amount: payment.amount,
        status: payment.status,
        paidAt: payment.paidAt
      },
      receipt
    });

  } catch (error) {
    await transaction.rollback();
    console.error('Complete purchase error:', error);
    res.status(500).json({
      success: false,
      message: 'Purchase processing failed'
    });
  }
};

// Generate receipt for completed order
const generateReceipt = async (req, res) => {
  try {
    const { orderId } = req.params;
    const userId = req.user.id;

    // Fetch order with all details
    const order = await Order.findOne({
      where: {
        id: orderId,
        userId: userId
      },
      include: [
        {
          model: OrderItem,
          as: 'items',
          include: [{
            model: Product,
            attributes: ['id', 'name', 'brand', 'sku']
          }]
        },
        {
          model: User,
          attributes: ['id', 'name', 'email', 'phone']
        },
        {
          model: Payment,
          attributes: ['id', 'transactionId', 'paymentMethod', 'amount', 'paidAt', 'status']
        }
      ]
    });

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    if (order.paymentStatus !== 'paid') {
      return res.status(400).json({
        success: false,
        message: 'Receipt can only be generated for paid orders'
      });
    }

    // Generate receipt data
    const receipt = {
      receiptNumber: `RCP-${order.orderNumber}`,
      orderNumber: order.orderNumber,
      orderDate: order.createdAt,
      paymentDate: order.Payments?.[0]?.paidAt || order.createdAt,

      // Customer information
      customer: {
        name: order.User.name,
        email: order.User.email,
        phone: order.User.phone
      },

      // Shipping address
      shippingAddress: order.shippingAddress,
      billingAddress: order.billingAddress,

      // Order items
      items: order.items.map(item => ({
        productId: item.Product.id,
        productName: item.Product.name,
        brand: item.Product.brand,
        sku: item.Product.sku,
        quantity: item.quantity,
        unitPrice: parseFloat(item.price),
        color: item.color,
        size: item.size,
        total: parseFloat(item.price) * item.quantity
      })),

      // Payment information
      payment: {
        method: order.paymentMethod,
        transactionId: order.Payments?.[0]?.transactionId,
        status: order.Payments?.[0]?.status
      },

      // Order totals
      totals: {
        subtotal: parseFloat(order.subtotal),
        shippingCost: parseFloat(order.shippingCost),
        tax: parseFloat(order.tax),
        total: parseFloat(order.total)
      },

      // Order status
      orderStatus: order.orderStatus,
      paymentStatus: order.paymentStatus,

      // Generated timestamp
      generatedAt: new Date()
    };

    res.json({
      success: true,
      receipt
    });

  } catch (error) {
    console.error('Generate receipt error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate receipt'
    });
  }
};

module.exports = {
  processPayment,
  getPaymentDetails,
  getUserPayments,
  refundPayment,
  completePurchase,
  generateReceipt
};
