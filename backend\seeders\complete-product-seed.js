require('dotenv').config();
const { connectDB } = require('../config/database');
const { Category, Product } = require('../models');

// Product data structure with exact quantities as specified
const categoryData = {
  'Women': {
    'T-shirt': 31,
    'Shirt': 43,
    'Jacket': 20,
    'Skirt': 17,
    'Trouser': 17,
    'Shorts': 19,
    'Jeans': 16,
    'Dress': 23,
    'Shoes': 20
  },
  'Men': {
    'T-shirt': 24,
    'Shirt': 22,
    'Jeans': 10,
    'Jacket': 19,
    'Trouser': 9,
    'Shoes': 17
  },
  'Boys': {
    'Clothing': 45,
    'Shoes': 17
  },
  'Girls': {
    'Clothing': 46,
    'Shoes': 20
  },
  'Accessories': {
    'Glasses': 19,
    'Watches': 6,
    'Gloves': 5,
    'Belt': 9,
    'Hat': 16,
    'Bag': 33,
    'Wallet': 8
  }
};

// Random data generators
const colors = ['Black', 'White', 'Red', 'Blue', '<PERSON>', 'Yellow', 'Pink', 'Purple', 'Orange', '<PERSON>', '<PERSON>', '<PERSON>', 'Beige', 'Maroon', 'Teal'];
const sizes = ['XS', 'S', 'M', 'L', 'XL', 'XXL'];
const shoeSizes = ['6', '7', '8', '9', '10', '11', '12'];
const accessorySizes = ['One Size', 'Small', 'Medium', 'Large'];

const brands = ['StyleCo', 'FashionHub', 'TrendWear', 'ClassicStyle', 'ModernFit', 'UrbanWear', 'ChicBoutique', 'ElegantStyle'];

const materials = {
  'T-shirt': ['Cotton', '100% Cotton', 'Cotton Blend', 'Organic Cotton'],
  'Shirt': ['Cotton', 'Linen', 'Silk', 'Cotton Blend', 'Polyester'],
  'Jacket': ['Wool', 'Leather', 'Denim', 'Polyester', 'Cotton'],
  'Jeans': ['Denim', '100% Cotton Denim', 'Stretch Denim'],
  'Dress': ['Cotton', 'Silk', 'Chiffon', 'Polyester', 'Linen'],
  'Shoes': ['Leather', 'Canvas', 'Synthetic', 'Suede'],
  'default': ['Cotton', 'Polyester', 'Cotton Blend']
};

const getRandomElement = (array) => array[Math.floor(Math.random() * array.length)];
const getRandomElements = (array, count) => {
  const shuffled = [...array].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
};

const generateProductName = (category, subcategory, index) => {
  const adjectives = ['Classic', 'Modern', 'Vintage', 'Premium', 'Casual', 'Elegant', 'Stylish', 'Comfortable', 'Trendy', 'Chic'];
  const styles = {
    'T-shirt': ['Crew Neck', 'V-Neck', 'Polo', 'Graphic', 'Plain'],
    'Shirt': ['Button-Down', 'Casual', 'Formal', 'Oxford', 'Flannel'],
    'Jacket': ['Blazer', 'Bomber', 'Denim', 'Leather', 'Windbreaker'],
    'Jeans': ['Skinny', 'Straight', 'Bootcut', 'Wide Leg', 'High Waist'],
    'Dress': ['Maxi', 'Mini', 'Midi', 'A-Line', 'Wrap'],
    'Shoes': ['Sneakers', 'Boots', 'Loafers', 'Sandals', 'Heels'],
    'default': ['Style', 'Design', 'Collection']
  };
  
  const adj = getRandomElement(adjectives);
  const style = getRandomElement(styles[subcategory] || styles.default);
  return `${adj} ${style} ${subcategory}`;
};

const generatePrice = () => {
  const basePrice = Math.random() * 150 + 20; // $20-$170
  const originalPrice = Math.round(basePrice * 100) / 100;
  const discountPercent = Math.floor(Math.random() * 50); // 0-50% discount
  const salePrice = Math.round(originalPrice * (100 - discountPercent) / 100 * 100) / 100;
  return { originalPrice, salePrice, discount: discountPercent };
};

const getSizesForCategory = (subcategory) => {
  if (subcategory.toLowerCase().includes('shoe')) {
    return getRandomElements(shoeSizes, 3 + Math.floor(Math.random() * 4));
  } else if (['Glasses', 'Watches', 'Hat', 'Bag', 'Wallet'].includes(subcategory)) {
    return getRandomElements(accessorySizes, 1 + Math.floor(Math.random() * 2));
  } else {
    return getRandomElements(sizes, 3 + Math.floor(Math.random() * 4));
  }
};

const seedProducts = async () => {
  try {
    await connectDB();
    console.log('✅ Database connected');

    // Clear existing data
    await Product.destroy({ where: {} });
    await Category.destroy({ where: {} });
    console.log('✅ Cleared existing data');

    const categoryMap = new Map();
    const subcategoryMap = new Map();

    // Create categories and subcategories
    for (const [categoryName, subcategories] of Object.entries(categoryData)) {
      const category = await Category.create({
        name: categoryName,
        description: `${categoryName} clothing and accessories`,
        slug: categoryName.toLowerCase(),
        isActive: true
      });
      
      categoryMap.set(categoryName, category);
      console.log(`✅ Created category: ${categoryName}`);

      // Create subcategories
      for (const subcategoryName of Object.keys(subcategories)) {
        const subcategory = await Category.create({
          name: `${categoryName} ${subcategoryName}`,
          description: `${subcategoryName} in ${categoryName} category`,
          slug: `${categoryName.toLowerCase()}-${subcategoryName.toLowerCase()}`,
          parentId: category.id,
          isActive: true
        });
        
        subcategoryMap.set(`${categoryName}-${subcategoryName}`, subcategory);
        console.log(`  ✅ Created subcategory: ${categoryName} ${subcategoryName}`);
      }
    }

    // Generate products
    let totalProducts = 0;
    for (const [categoryName, subcategories] of Object.entries(categoryData)) {
      const category = categoryMap.get(categoryName);
      
      for (const [subcategoryName, quantity] of Object.entries(subcategories)) {
        const subcategory = subcategoryMap.get(`${categoryName}-${subcategoryName}`);
        console.log(`\n🔄 Generating ${quantity} products for ${categoryName} ${subcategoryName}...`);
        
        const products = [];
        for (let i = 1; i <= quantity; i++) {
          const { originalPrice, salePrice, discount } = generatePrice();
          const productColors = getRandomElements(colors, 2 + Math.floor(Math.random() * 4));
          const productSizes = getSizesForCategory(subcategoryName);
          const material = getRandomElement(materials[subcategoryName] || materials.default);
          
          const productName = generateProductName(categoryName, subcategoryName, i);
          const product = {
            name: productName,
            description: `High-quality ${subcategoryName.toLowerCase()} perfect for ${categoryName.toLowerCase()}. Made with premium ${material.toLowerCase()} for comfort and style.`,
            originalPrice,
            salePrice,
            discount,
            categoryId: category.id,
            subcategoryId: subcategory.id,
            images: JSON.stringify([`${categoryName.toLowerCase()}_${subcategoryName.toLowerCase()}${i}.jpg`]),
            colors: JSON.stringify(productColors),
            sizes: JSON.stringify(productSizes),
            stock: Math.floor(Math.random() * 100) + 10, // 10-109 stock
            sku: `${categoryName.substring(0,2).toUpperCase()}${subcategoryName.substring(0,2).toUpperCase()}${String(i).padStart(3, '0')}`,
            brand: getRandomElement(brands),
            material,
            tags: JSON.stringify([categoryName.toLowerCase(), subcategoryName.toLowerCase(), 'fashion']),
            ratingAverage: Math.round((Math.random() * 2 + 3) * 10) / 10, // 3.0-5.0
            ratingCount: Math.floor(Math.random() * 50) + 5, // 5-54 reviews
            isActive: true,
            isFeatured: Math.random() > 0.8, // 20% chance of being featured
            slug: productName.toLowerCase().replace(/[^a-zA-Z0-9]/g, '-').replace(/-+/g, '-')
          };
          
          products.push(product);
        }
        
        await Product.bulkCreate(products);
        totalProducts += quantity;
        console.log(`  ✅ Created ${quantity} products for ${categoryName} ${subcategoryName}`);
      }
    }

    console.log(`\n🎉 Seed completed successfully!`);
    console.log(`📊 Total products created: ${totalProducts}`);
    console.log(`📊 Total categories: ${Object.keys(categoryData).length}`);
    console.log(`📊 Total subcategories: ${Object.values(categoryData).reduce((sum, subs) => sum + Object.keys(subs).length, 0)}`);
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Seed failed:', error);
    process.exit(1);
  }
};

seedProducts();
