# Backend Connection Troubleshooting Commands
# Save as: troubleshooting_commands.md

## 🔍 DIAGNOSTIC COMMANDS

### Check if Backend is Running
```powershell
# Check what's running on port 3001
netstat -an | findstr :3001

# Check all Node.js processes
Get-Process node -ErrorAction SilentlyContinue

# Test backend health
curl http://localhost:3001/api/health
# OR
Invoke-WebRequest -Uri "http://localhost:3001/api/health"
```

### Kill Processes on Port 3001
```powershell
# Find process using port 3001
netstat -ano | findstr :3001

# Kill specific process (replace PID with actual process ID)
taskkill /PID [PID] /F

# Kill all Node.js processes (be careful!)
Get-Process node | Stop-Process -Force
```

### Check Network Connectivity
```powershell
# Test if localhost resolves
ping localhost

# Test port connectivity
Test-NetConnection -ComputerName localhost -Port 3001

# Check Windows Firewall
Get-NetFirewallRule -DisplayName "*Node*" | Format-Table
```

## 🚀 RESTART COMMANDS

### Proper Backend Restart
```powershell
# Navigate to backend directory
cd "d:\Back-end\PROJECT_BACKEND\backend"

# Kill existing processes
netstat -ano | findstr :3001 | ForEach-Object { $pid = ($_ -split '\s+')[-1]; Stop-Process -Id $pid -Force }

# Install dependencies (if needed)
npm install

# Start backend server
npm start
```

### Frontend Restart
```powershell
# Navigate to frontend directory
cd "d:\Back-end\PROJECT_BACKEND\frontend"

# Start frontend server
npm run dev
```

### Both Servers (Parallel)
```powershell
# Open two PowerShell windows and run:

# Terminal 1 - Backend
cd "d:\Back-end\PROJECT_BACKEND\backend"
npm start

# Terminal 2 - Frontend  
cd "d:\Back-end\PROJECT_BACKEND\frontend"
npm run dev
```

## 🔧 DEBUGGING COMMANDS

### Check Environment Variables
```powershell
# In backend directory
Get-Content .env

# Check if .env is loaded
node -e "require('dotenv').config(); console.log('PORT:', process.env.PORT);"
```

### Test CORS Configuration
```powershell
# Test preflight request
curl -X OPTIONS http://localhost:3001/api/auth/login -H "Origin: http://localhost:3000" -H "Access-Control-Request-Method: POST" -H "Access-Control-Request-Headers: Content-Type,Authorization" -v
```

### Check Logs
```powershell
# Monitor backend logs (if using PM2)
pm2 logs

# Check Windows Event Viewer for Node.js errors
Get-EventLog -LogName Application -Source "Node.js" -Newest 10
```

### Database Connection Test
```powershell
# In backend directory
node -e "
const { sequelize } = require('./config/database');
sequelize.authenticate()
  .then(() => console.log('✅ Database connected'))
  .catch(err => console.log('❌ Database error:', err.message));
"
```

## 🛠️ QUICK FIXES

### Fix Common Issues
```powershell
# 1. Port already in use
netstat -ano | findstr :3001
# Kill the process using the port

# 2. Permission denied
# Run PowerShell as Administrator

# 3. Node modules corrupted
cd "d:\Back-end\PROJECT_BACKEND\backend"
Remove-Item node_modules -Recurse -Force
Remove-Item package-lock.json -Force
npm install

# 4. Clear npm cache
npm cache clean --force

# 5. Reset git (if needed)
git clean -fd
git reset --hard HEAD
```

### Environment Reset
```powershell
# Reset .env file
cd "d:\Back-end\PROJECT_BACKEND\backend"
Copy-Item .env.example .env  # If .env.example exists

# OR create new .env
@"
NODE_ENV=development
PORT=3001
DB_HOST=mysql-17f276b8-chuthomey3-12ec.e.aivencloud.com
DB_PORT=10392
DB_NAME=clothes
DB_USER=avnadmin
DB_PASSWORD=AVNS_YLX_gg3pyvyy7t_F78G
JWT_SECRET=your_jwt_secret_key_here
CORS_ORIGIN=http://localhost:3000
"@ | Out-File -FilePath ".env" -Encoding utf8
```

## 📊 MONITORING COMMANDS

### Real-time Monitoring
```powershell
# Monitor port 3001 continuously
while ($true) { 
  netstat -an | findstr :3001; 
  Start-Sleep 5; 
  Clear-Host 
}

# Monitor backend health
while ($true) { 
  try { 
    Invoke-WebRequest -Uri "http://localhost:3001/api/health" -TimeoutSec 2; 
    Write-Host "✅ Backend OK" 
  } catch { 
    Write-Host "❌ Backend Down" 
  }; 
  Start-Sleep 10 
}
```

### System Resource Check
```powershell
# Check CPU and Memory usage
Get-Process node | Format-Table -Property ProcessName, CPU, WorkingSet, PagedMemorySize

# Check disk space
Get-WmiObject -Class Win32_LogicalDisk | Select-Object DeviceID, @{Name="Size(GB)";Expression={[math]::Round($_.Size/1GB,2)}}, @{Name="FreeSpace(GB)";Expression={[math]::Round($_.FreeSpace/1GB,2)}}
```

## 🔄 AUTOMATED SCRIPTS

Use the PowerShell scripts created:
- `check_backend.ps1` - Comprehensive connection check
- `restart_backend.ps1` - Proper backend restart

```powershell
# Run diagnostic script
.\check_backend.ps1

# Run restart script
.\restart_backend.ps1
```
