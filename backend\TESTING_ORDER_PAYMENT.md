# 🧪 Manual Order & Payment Testing Guide

## Prerequisites
1. ✅ Backend server running on port 3001
2. ✅ Database connected and seeded with products
3. ✅ Frontend running on port 5174

## Test Scenarios

### 🔐 **Scenario 1: User Authentication**

#### Registration Test:
```bash
curl -X POST http://localhost:3001/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Customer",
    "email": "<EMAIL>",
    "password": "password123",
    "phone": "+1234567890"
  }'
```

**Expected Response:**
```json
{
  "success": true,
  "message": "User registered successfully",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": 123,
    "name": "Test Customer",
    "email": "<EMAIL>",
    "role": "customer"
  }
}
```

#### Login Test:
```bash
curl -X POST http://localhost:3001/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

### 🛍️ **Scenario 2: Product Search & Selection**

#### Get All Products:
```bash
curl "http://localhost:3001/api/products?limit=10"
```

#### Search Products:
```bash
curl "http://localhost:3001/api/products/search?q=shirt&category=women"
```

#### Get Product Details:
```bash
curl "http://localhost:3001/api/products/1"
```

### 📦 **Scenario 3: Order Creation**

#### Create Order with Payment:
```bash
curl -X POST http://localhost:3001/api/orders \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -d '{
    "items": [
      {
        "productId": 1,
        "quantity": 2,
        "color": "Black",
        "size": "M"
      },
      {
        "productId": 2,
        "quantity": 1,
        "color": "Blue",
        "size": "L"
      }
    ],
    "shippingAddress": {
      "fullName": "Test Customer",
      "address": "123 Test Street",
      "city": "Test City",
      "state": "TC",
      "zipCode": "12345",
      "country": "USA",
      "phone": "+1234567890"
    },
    "paymentMethod": "credit_card",
    "paymentDetails": {
      "cardNumber": "****************",
      "expiryMonth": "12",
      "expiryYear": "2025",
      "cvv": "123",
      "cardholderName": "Test Customer"
    }
  }'
```

**Expected Response:**
```json
{
  "success": true,
  "message": "Order created and payment processed successfully",
  "data": {
    "order": {
      "id": 456,
      "orderNumber": "ORD-1642345678-123",
      "total": "89.98",
      "orderStatus": "confirmed",
      "paymentStatus": "paid",
      "items": [...]
    },
    "payment": {
      "transactionId": "TXN-1642345678-4567",
      "amount": "89.98",
      "status": "completed"
    }
  }
}
```

### 📋 **Scenario 4: Order Verification**

#### Get Order Details:
```bash
curl -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  "http://localhost:3001/api/orders/456"
```

#### Get Order History:
```bash
curl -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  "http://localhost:3001/api/orders/history?page=1&limit=10"
```

#### Verify Payment:
```bash
curl -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  "http://localhost:3001/api/verification/payment/verify/456"
```

### 💳 **Scenario 5: Different Payment Methods**

#### Credit Card Payment:
```json
{
  "paymentMethod": "credit_card",
  "paymentDetails": {
    "cardNumber": "****************",
    "expiryMonth": "12",
    "expiryYear": "2025",
    "cvv": "123",
    "cardholderName": "Test Customer"
  }
}
```

#### PayPal Payment:
```json
{
  "paymentMethod": "paypal",
  "paymentDetails": {
    "paypalEmail": "<EMAIL>"
  }
}
```

#### Cash on Delivery:
```json
{
  "paymentMethod": "cash_on_delivery",
  "paymentDetails": {}
}
```

## 🔍 Database Verification Queries

### Check Order Data:
```sql
-- Get latest order with all details
SELECT 
    o.id,
    o.orderNumber,
    o.total,
    o.orderStatus,
    o.paymentStatus,
    u.name as customerName,
    u.email as customerEmail,
    o.createdAt
FROM orders o
JOIN users u ON o.userId = u.id
ORDER BY o.createdAt DESC
LIMIT 1;

-- Get order items for latest order
SELECT 
    oi.productName,
    oi.quantity,
    oi.price,
    oi.color,
    oi.size,
    p.name as actualProductName,
    p.originalPrice
FROM orderitems oi
JOIN products p ON oi.productId = p.id
WHERE oi.orderId = (SELECT MAX(id) FROM orders)
ORDER BY oi.id;

-- Get payment details for latest order
SELECT 
    p.transactionId,
    p.amount,
    p.paymentMethod,
    p.status,
    p.paidAt,
    o.orderNumber
FROM payments p
JOIN orders o ON p.orderId = o.id
WHERE o.id = (SELECT MAX(id) FROM orders);
```

### Verify Data Consistency:
```sql
-- Check if order total matches payment amount
SELECT 
    o.orderNumber,
    o.total as orderTotal,
    p.amount as paymentAmount,
    (o.total - p.amount) as difference,
    CASE 
        WHEN ABS(o.total - p.amount) < 0.01 THEN 'MATCH'
        ELSE 'MISMATCH'
    END as verification
FROM orders o
JOIN payments p ON o.id = p.orderId
ORDER BY o.createdAt DESC
LIMIT 5;

-- Check stock updates after order
SELECT 
    p.name,
    p.stock as currentStock,
    COALESCE(SUM(oi.quantity), 0) as totalOrdered
FROM products p
LEFT JOIN orderitems oi ON p.id = oi.productId
GROUP BY p.id, p.name
HAVING totalOrdered > 0
ORDER BY totalOrdered DESC;
```

## 🎯 Frontend Testing Steps

### 1. **Registration/Login Flow:**
1. Go to http://localhost:5174
2. Click "Register" or "Login"
3. Fill in credentials
4. Verify user data is stored
5. Check localStorage for token

### 2. **Shopping Flow:**
1. Browse products on homepage
2. Search for specific items
3. Add items to cart
4. View cart contents
5. Proceed to checkout

### 3. **Checkout Flow:**
1. Enter shipping address
2. Select payment method
3. Enter payment details
4. Review order summary
5. Submit order

### 4. **Order Confirmation:**
1. Verify order confirmation page loads
2. Check order details display correctly
3. Verify receipt information
4. Test "Download Receipt" if available

### 5. **Order History:**
1. Navigate to "My Orders" or "Order History"
2. Verify orders display correctly
3. Check order status and details
4. Test order filtering by status

## 🚨 Error Testing Scenarios

### 1. **Invalid Product ID:**
```bash
curl -X POST http://localhost:3001/api/orders \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"items":[{"productId":99999,"quantity":1}]}'
```

### 2. **Insufficient Stock:**
```bash
curl -X POST http://localhost:3001/api/orders \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"items":[{"productId":1,"quantity":1000}]}'
```

### 3. **Invalid Payment Details:**
```bash
curl -X POST http://localhost:3001/api/orders \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"items":[{"productId":1,"quantity":1}],"paymentMethod":"credit_card","paymentDetails":{"cardNumber":"invalid"}}'
```

### 4. **Unauthorized Access:**
```bash
curl -X POST http://localhost:3001/api/orders \
  -d '{"items":[{"productId":1,"quantity":1}]}'
```

## ✅ Success Criteria

### Order Creation:
- ✅ Order record created in database
- ✅ Order items saved with correct product details
- ✅ Payment record created
- ✅ Product stock updated
- ✅ Order number generated
- ✅ Timestamps recorded correctly

### Payment Processing:
- ✅ Transaction ID generated
- ✅ Payment amount matches order total
- ✅ Payment status updated correctly
- ✅ Payment method recorded

### Data Integrity:
- ✅ Foreign key relationships maintained
- ✅ No orphaned records
- ✅ Data consistency across tables
- ✅ Proper error handling

### API Responses:
- ✅ Correct HTTP status codes
- ✅ Consistent response format
- ✅ Proper error messages
- ✅ Complete data returned

## 🔧 Troubleshooting Common Issues

### "Product not found" Error:
```sql
-- Check if products exist
SELECT id, name, stock FROM products WHERE isActive = 1 LIMIT 5;
```

### "Insufficient stock" Error:
```sql
-- Check product stock
SELECT id, name, stock FROM products WHERE id = YOUR_PRODUCT_ID;
```

### "Order creation failed" Error:
- Check authentication token
- Verify product IDs exist
- Check stock availability
- Validate required fields

### "Payment processing failed" Error:
- Check payment method is valid
- Verify payment details format
- Check database connection
- Review payment gateway configuration

This comprehensive testing guide ensures your order and payment system is working correctly! 🚀
