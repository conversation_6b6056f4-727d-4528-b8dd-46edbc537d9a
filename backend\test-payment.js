const axios = require('axios');

const testPayment = async () => {
  try {
    // First login to get token
    const loginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      email: '<EMAIL>',
      password: 'password123'
    });

    const token = loginResponse.data.token;
    console.log('✅ Login successful, token:', token);

    // Get a product to purchase
    const productsResponse = await axios.get('http://localhost:3001/api/products?limit=1');
    const product = productsResponse.data.products[0];
    console.log('✅ Product found:', product.name, 'Price:', product.salePrice);

    // Test payment
    const paymentData = {
      items: [
        {
          productId: product.id,
          quantity: 1,
          color: 'black',
          size: 'M',
          price: parseFloat(product.salePrice)
        }
      ],
      shippingAddress: {
        firstName: 'Test',
        lastName: 'User',
        email: '<EMAIL>',
        address: '123 Test St',
        city: 'Test City',
        zipCode: '12345',
        country: 'USA',
        phone: '555-1234'
      },
      billingAddress: {
        firstName: 'Test',
        lastName: 'User',
        email: '<EMAIL>',
        address: '123 Test St',
        city: 'Test City',
        zipCode: '12345',
        country: 'USA',
        phone: '555-1234'
      },
      paymentMethod: 'credit_card',
      paymentGateway: 'manual',
      transactionId: `TXN-${Date.now()}-${Math.floor(Math.random() * 10000)}`
    };

    console.log('Testing payment with data:', JSON.stringify(paymentData, null, 2));

    const paymentResponse = await axios.post('http://localhost:3001/api/payments/purchase', paymentData, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('✅ Payment successful:', paymentResponse.data);
  } catch (error) {
    console.error('❌ Payment error:', error.response?.data || error.message);
    if (error.response?.status === 400) {
      console.log('Full error response:', JSON.stringify(error.response.data, null, 2));
    }
  }
};

testPayment();
