const axios = require('axios');

const testPayment = async () => {
  try {
    // First register a new user to get token
    const registerResponse = await axios.post('http://localhost:3001/api/auth/register', {
      name: 'Payment Test User',
      email: `test_payment_${Date.now()}@example.com`,
      password: 'password123'
    });

    const token = registerResponse.data.token;
    console.log('✅ Registration successful, token:', token);

    // Get a product to purchase
    const productsResponse = await axios.get('http://localhost:3001/api/products?limit=1');
    const product = productsResponse.data.products[0];
    console.log('✅ Product found:', product.name, 'Price:', product.salePrice);

    // Test payment
    const paymentData = {
      items: [
        {
          productId: product.id,
          quantity: 1,
          color: 'black',
          size: 'M',
          price: parseFloat(product.salePrice)
        }
      ],
      shippingAddress: {
        firstName: 'Test',
        lastName: 'User',
        email: registerResponse.data.user.email,
        address: '123 Test St',
        city: 'Test City',
        zipCode: '12345',
        country: 'USA',
        phone: '555-1234'
      },
      billingAddress: {
        firstName: 'Test',
        lastName: 'User',
        email: registerResponse.data.user.email,
        address: '123 Test St',
        city: 'Test City',
        zipCode: '12345',
        country: 'USA',
        phone: '555-1234'
      },
      paymentMethod: 'credit_card',
      paymentGateway: 'manual',
      transactionId: `TXN-${Date.now()}-${Math.floor(Math.random() * 10000)}`
    };

    console.log('Testing payment with data:', JSON.stringify(paymentData, null, 2));

    const paymentResponse = await axios.post('http://localhost:3001/api/payments/purchase', paymentData, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('✅ Payment successful:', paymentResponse.data);
  } catch (error) {
    console.error('❌ Payment error:', error.response?.data || error.message);
    if (error.response?.status === 400) {
      console.log('Full error response:', JSON.stringify(error.response.data, null, 2));
    }
  }
};

testPayment();
