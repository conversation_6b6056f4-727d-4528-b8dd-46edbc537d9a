// Order & Payment Testing Script
// File: backend/test-order-payment.js

require('dotenv').config();
const axios = require('axios');

const API_BASE = 'http://localhost:3001/api';

// Test data
const testUser = {
  name: 'Test Customer',
  email: '<EMAIL>',
  password: 'password123',
  phone: '+1234567890',
  address: '123 Test Street, Test City, TC 12345'
};

const testOrder = {
  items: [
    {
      productId: 1,
      quantity: 2,
      color: 'Black',
      size: 'M'
    },
    {
      productId: 2,
      quantity: 1,
      color: 'Blue',
      size: 'L'
    }
  ],
  shippingAddress: {
    fullName: 'Test Customer',
    address: '123 Test Street',
    city: 'Test City',
    state: 'TC',
    zipCode: '12345',
    country: 'USA',
    phone: '+1234567890'
  },
  paymentMethod: 'credit_card',
  paymentDetails: {
    cardNumber: '****************',
    expiryMonth: '12',
    expiryYear: '2025',
    cvv: '123',
    cardholderName: 'Test Customer'
  }
};

let authToken = '';
let userId = '';
let createdOrderId = '';

// Helper function to make authenticated requests
const makeRequest = async (method, endpoint, data = null) => {
  const config = {
    method,
    url: `${API_BASE}${endpoint}`,
    headers: {
      'Content-Type': 'application/json',
      ...(authToken && { 'Authorization': `Bearer ${authToken}` })
    }
  };
  
  if (data) {
    config.data = data;
  }
  
  return axios(config);
};

// Test 1: User Registration
const testUserRegistration = async () => {
  console.log('\n🔐 Test 1: User Registration');
  console.log('=' * 50);
  
  try {
    const response = await makeRequest('POST', '/auth/register', testUser);
    
    if (response.data.success) {
      authToken = response.data.token;
      userId = response.data.user.id;
      console.log('✅ User registration successful');
      console.log(`   User ID: ${userId}`);
      console.log(`   Email: ${response.data.user.email}`);
      console.log(`   Token: ${authToken.substring(0, 20)}...`);
      return true;
    } else {
      console.log('❌ User registration failed:', response.data.message);
      return false;
    }
  } catch (error) {
    if (error.response?.status === 400 && error.response.data.message.includes('already exists')) {
      console.log('ℹ️ User already exists, attempting login...');
      return await testUserLogin();
    }
    console.error('❌ Registration error:', error.response?.data || error.message);
    return false;
  }
};

// Test 2: User Login
const testUserLogin = async () => {
  console.log('\n🔑 Test 2: User Login');
  console.log('=' * 50);
  
  try {
    const response = await makeRequest('POST', '/auth/login', {
      email: testUser.email,
      password: testUser.password
    });
    
    if (response.data.success) {
      authToken = response.data.token;
      userId = response.data.user.id;
      console.log('✅ User login successful');
      console.log(`   User ID: ${userId}`);
      console.log(`   Name: ${response.data.user.name}`);
      return true;
    } else {
      console.log('❌ User login failed:', response.data.message);
      return false;
    }
  } catch (error) {
    console.error('❌ Login error:', error.response?.data || error.message);
    return false;
  }
};

// Test 3: Get Products for Order
const testGetProducts = async () => {
  console.log('\n🛍️ Test 3: Get Products');
  console.log('=' * 50);
  
  try {
    const response = await makeRequest('GET', '/products?limit=5');
    
    if (response.data.success && response.data.products.length > 0) {
      console.log('✅ Products retrieved successfully');
      console.log(`   Found ${response.data.products.length} products`);
      
      // Update test order with actual product IDs
      const products = response.data.products;
      testOrder.items = [
        {
          productId: products[0].id,
          quantity: 2,
          color: JSON.parse(products[0].colors || '["Black"]')[0],
          size: JSON.parse(products[0].sizes || '["M"]')[0]
        }
      ];
      
      if (products.length > 1) {
        testOrder.items.push({
          productId: products[1].id,
          quantity: 1,
          color: JSON.parse(products[1].colors || '["Blue"]')[0],
          size: JSON.parse(products[1].sizes || '["L"]')[0]
        });
      }
      
      console.log('   Test products:');
      testOrder.items.forEach((item, index) => {
        const product = products[index];
        console.log(`   - ${product.name} (ID: ${item.productId})`);
        console.log(`     Price: $${product.originalPrice}, Color: ${item.color}, Size: ${item.size}, Qty: ${item.quantity}`);
      });
      
      return true;
    } else {
      console.log('❌ No products found');
      return false;
    }
  } catch (error) {
    console.error('❌ Get products error:', error.response?.data || error.message);
    return false;
  }
};

// Test 4: Create Order
const testCreateOrder = async () => {
  console.log('\n📦 Test 4: Create Order');
  console.log('=' * 50);
  
  try {
    console.log('🔄 Creating order with items:');
    testOrder.items.forEach(item => {
      console.log(`   - Product ID: ${item.productId}, Qty: ${item.quantity}, Color: ${item.color}, Size: ${item.size}`);
    });
    
    const response = await makeRequest('POST', '/orders', testOrder);
    
    if (response.data.success) {
      createdOrderId = response.data.data.order.id;
      console.log('✅ Order created successfully');
      console.log(`   Order ID: ${createdOrderId}`);
      console.log(`   Order Number: ${response.data.data.order.orderNumber}`);
      console.log(`   Total: $${response.data.data.order.total}`);
      console.log(`   Status: ${response.data.data.order.orderStatus}`);
      console.log(`   Payment Status: ${response.data.data.order.paymentStatus}`);
      
      if (response.data.data.payment) {
        console.log(`   Transaction ID: ${response.data.data.payment.transactionId}`);
        console.log(`   Payment Amount: $${response.data.data.payment.amount}`);
        console.log(`   Payment Status: ${response.data.data.payment.status}`);
      }
      
      return true;
    } else {
      console.log('❌ Order creation failed:', response.data.message);
      return false;
    }
  } catch (error) {
    console.error('❌ Create order error:', error.response?.data || error.message);
    return false;
  }
};

// Test 5: Get Order Details
const testGetOrderDetails = async () => {
  console.log('\n📋 Test 5: Get Order Details');
  console.log('=' * 50);
  
  if (!createdOrderId) {
    console.log('❌ No order ID available');
    return false;
  }
  
  try {
    const response = await makeRequest('GET', `/orders/${createdOrderId}`);
    
    if (response.data.success) {
      const order = response.data.data;
      console.log('✅ Order details retrieved successfully');
      console.log(`   Order Number: ${order.orderNumber}`);
      console.log(`   Customer: ${order.customer.name} (${order.customer.email})`);
      console.log(`   Total: $${order.total}`);
      console.log(`   Status: ${order.orderStatus}`);
      console.log(`   Payment Method: ${order.paymentMethod}`);
      
      if (order.items && order.items.length > 0) {
        console.log(`   Items (${order.items.length}):`);
        order.items.forEach(item => {
          console.log(`   - ${item.productName} (${item.productBrand})`);
          console.log(`     Qty: ${item.quantity}, Price: $${item.price}, Color: ${item.color}, Size: ${item.size}`);
          console.log(`     Subtotal: $${item.subtotal}`);
        });
      }
      
      if (order.payment) {
        console.log('   Payment Details:');
        console.log(`   - Transaction ID: ${order.payment.transactionId}`);
        console.log(`   - Amount: $${order.payment.amount}`);
        console.log(`   - Status: ${order.payment.status}`);
        console.log(`   - Paid At: ${order.payment.paidAt}`);
      }
      
      return true;
    } else {
      console.log('❌ Failed to get order details:', response.data.message);
      return false;
    }
  } catch (error) {
    console.error('❌ Get order details error:', error.response?.data || error.message);
    return false;
  }
};

// Test 6: Get Order History
const testGetOrderHistory = async () => {
  console.log('\n📚 Test 6: Get Order History');
  console.log('=' * 50);
  
  try {
    const response = await makeRequest('GET', '/orders/history?limit=10');
    
    if (response.data.success) {
      const orders = response.data.data.orders;
      console.log('✅ Order history retrieved successfully');
      console.log(`   Total Orders: ${response.data.data.pagination.totalOrders}`);
      console.log(`   Current Page: ${response.data.data.pagination.currentPage}`);
      
      if (orders.length > 0) {
        console.log('   Recent Orders:');
        orders.forEach(order => {
          console.log(`   - ${order.orderNumber}: $${order.total} (${order.orderStatus})`);
          console.log(`     Created: ${new Date(order.createdAt).toLocaleDateString()}`);
          console.log(`     Items: ${order.orderItems?.length || 0}`);
        });
      } else {
        console.log('   No orders found');
      }
      
      return true;
    } else {
      console.log('❌ Failed to get order history:', response.data.message);
      return false;
    }
  } catch (error) {
    console.error('❌ Get order history error:', error.response?.data || error.message);
    return false;
  }
};

// Test 7: Payment Verification
const testPaymentVerification = async () => {
  console.log('\n💳 Test 7: Payment Verification');
  console.log('=' * 50);
  
  if (!createdOrderId) {
    console.log('❌ No order ID available for payment verification');
    return false;
  }
  
  try {
    const response = await makeRequest('GET', `/verification/payment/verify/${createdOrderId}`);
    
    if (response.data.success) {
      const verification = response.data.verification;
      console.log('✅ Payment verification successful');
      console.log(`   Order Exists: ${verification.orderExists ? '✅' : '❌'}`);
      console.log(`   Payment Record: ${verification.paymentRecord ? '✅' : '❌'}`);
      console.log(`   Amounts Match: ${verification.amountsMatch ? '✅' : '❌'}`);
      console.log(`   Verification Time: ${verification.timestamp}`);
      
      return true;
    } else {
      console.log('❌ Payment verification failed:', response.data.message);
      return false;
    }
  } catch (error) {
    console.error('❌ Payment verification error:', error.response?.data || error.message);
    return false;
  }
};

// Main test runner
const runAllTests = async () => {
  console.log('🧪 STARTING ORDER & PAYMENT TESTS');
  console.log('=' * 60);
  console.log(`API Base URL: ${API_BASE}`);
  console.log(`Test Time: ${new Date().toISOString()}`);
  
  const results = {
    userRegistration: false,
    userLogin: false,
    getProducts: false,
    createOrder: false,
    getOrderDetails: false,
    getOrderHistory: false,
    paymentVerification: false
  };
  
  // Run tests sequentially
  results.userRegistration = await testUserRegistration();
  if (results.userRegistration || authToken) {
    results.getProducts = await testGetProducts();
    if (results.getProducts) {
      results.createOrder = await testCreateOrder();
      if (results.createOrder) {
        results.getOrderDetails = await testGetOrderDetails();
        results.getOrderHistory = await testGetOrderHistory();
        results.paymentVerification = await testPaymentVerification();
      }
    }
  }
  
  // Test summary
  console.log('\n📊 TEST RESULTS SUMMARY');
  console.log('=' * 60);
  
  const testNames = {
    userRegistration: 'User Registration/Login',
    getProducts: 'Get Products',
    createOrder: 'Create Order',
    getOrderDetails: 'Get Order Details',
    getOrderHistory: 'Get Order History',
    paymentVerification: 'Payment Verification'
  };
  
  let passedTests = 0;
  let totalTests = Object.keys(results).length;
  
  Object.entries(results).forEach(([key, passed]) => {
    const status = passed ? '✅ PASS' : '❌ FAIL';
    console.log(`${status} - ${testNames[key]}`);
    if (passed) passedTests++;
  });
  
  console.log('\n' + '=' * 60);
  console.log(`TESTS PASSED: ${passedTests}/${totalTests}`);
  console.log(`SUCCESS RATE: ${((passedTests/totalTests) * 100).toFixed(1)}%`);
  
  if (passedTests === totalTests) {
    console.log('🎉 ALL TESTS PASSED! Your order & payment system is working correctly.');
  } else {
    console.log('⚠️ Some tests failed. Please check the errors above and ensure:');
    console.log('1. Backend server is running on port 3001');
    console.log('2. Database is connected and seeded with products');
    console.log('3. All API routes are properly configured');
    console.log('4. Authentication middleware is working');
  }
  
  console.log('\n🔗 Next Steps:');
  console.log('1. Test the frontend order flow');
  console.log('2. Verify order data in database');
  console.log('3. Test with different payment methods');
  console.log('4. Test edge cases (insufficient stock, invalid products, etc.)');
};

// Export for use in other files
module.exports = {
  runAllTests,
  testCreateOrder,
  testGetOrderDetails,
  testPaymentVerification
};

// Run tests if called directly
if (require.main === module) {
  runAllTests()
    .then(() => {
      console.log('\n✅ Test execution completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Test execution failed:', error);
      process.exit(1);
    });
}
