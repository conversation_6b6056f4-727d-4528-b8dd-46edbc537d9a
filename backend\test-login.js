const axios = require('axios');

const testLogin = async () => {
  try {
    const response = await axios.post('http://localhost:3001/api/auth/login', {
      email: '<EMAIL>',
      password: 'password123'
    });

    const data = response.data;
    console.log('Login response:', data);

    if (data.token) {
      console.log('✅ Login successful, token:', data.token);
      return data.token;
    } else {
      console.log('❌ Login failed');
    }
  } catch (error) {
    console.error('❌ Login error:', error.response?.data || error.message);
  }
};

testLogin();
