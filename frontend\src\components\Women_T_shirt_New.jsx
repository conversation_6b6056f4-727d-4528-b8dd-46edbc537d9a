import React, { useState, useEffect } from 'react';
import { Heart, Bell, ShoppingBag, User, LogOut } from 'lucide-react';
import { Link, useNavigate } from "react-router-dom";
import '../styles/Women_T_shirt.css';
import Footer from '../components/Footer';
import apiService from '../services/api';

const Women_T_shirt = () => {
  const navigate = useNavigate();
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [user, setUser] = useState(null);

  // Check if user is logged in
  useEffect(() => {
    const token = localStorage.getItem('token');
    const userData = localStorage.getItem('user');
    
    if (token && userData) {
      try {
        setUser(JSON.parse(userData));
      } catch (err) {
        console.error('Error parsing user data:', err);
        localStorage.removeItem('user');
        localStorage.removeItem('token');
      }
    }
  }, []);

  const handleLogout = () => {
    apiService.logout();
    setUser(null);
    navigate('/');
  };

  // Fetch women's t-shirts from API
  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setLoading(true);
        const response = await apiService.getProducts({
          subcategory: 'women-t-shirt',
          limit: 50
        });

        if (response && response.products) {
          // Process the products to match the expected format
          const processedProducts = response.products.map(product => ({
            ...product,
            colors: typeof product.colors === 'string' ? JSON.parse(product.colors) : product.colors,
            sizes: typeof product.sizes === 'string' ? JSON.parse(product.sizes) : product.sizes,
            images: typeof product.images === 'string' ? JSON.parse(product.images) : product.images,
            image: product.images ? (typeof product.images === 'string' ? JSON.parse(product.images)[0] : product.images[0]) : '/placeholder-image.jpg'
          }));
          
          setProducts(processedProducts);
        } else {
          setError('Failed to fetch products');
        }
      } catch (err) {
        console.error('Error fetching products:', err);
        setError('Failed to load products. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, []);

  const handleProductClick = (product) => {
    navigate('/payment', { state: { product } });
  };

  if (loading) {
    return (
      <div className="women-t-shirt-page">
        <div className="loading-container">
          <div className="loading">Loading Women's T-Shirts...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="women-t-shirt-page">
        <div className="error-container">
          <div className="error">{error}</div>
          <button onClick={() => window.location.reload()} className="retry-btn">
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="women-t-shirt-page">
      {/* Header */}
      <div className="header">
        <div className="logo-container">
          <Link to="/" className="logo-bg">
            <span className="logo-text">
              <span className="logo-star">★</span>
              StyleStore
            </span>
          </Link>
        </div>
        <div className="header-right">
          <div className="header-row">
            <input
              type="text"
              placeholder="Search"
              className="search-input"
            />
            <div className="icons-area">
              <Bell size={20} className="text-black" />
              <Heart size={20} className="text-black" />
              <div className="shopping-bag-rel">
                <ShoppingBag size={22} className="text-black" />
                <span className="shopping-bag-badge">0</span>
              </div>
              
              {user ? (
                <div className="user-section" style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
                  <div className="user-info" style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <User size={20} className="text-black" />
                    <span style={{ color: '#000', fontSize: '14px' }}>
                      Hi, {user.name}
                    </span>
                  </div>
                  <button 
                    onClick={handleLogout}
                    className="logout-btn"
                    style={{
                      background: 'none',
                      border: 'none',
                      cursor: 'pointer',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '4px',
                      padding: '4px 8px',
                      borderRadius: '4px',
                      color: '#ef4444'
                    }}
                  >
                    <LogOut size={16} />
                    <span style={{ fontSize: '12px' }}>Logout</span>
                  </button>
                </div>
              ) : (
                <>
                  <Link to="/login" className="login-link">LOGIN</Link>
                  <Link to="/register" className="register-link">REGISTER</Link>
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <div className="nav-container">
        <div className="nav-links">
          <Link to="/" className="nav-link">HOME</Link>
          <Link to="/collection" className="nav-link">COLLECTION</Link>
          <Link to="/women" className="nav-link">WOMEN</Link>
          <Link to="/men" className="nav-link">MEN</Link>
          <Link to="/boys" className="nav-link">BOYS</Link>
          <Link to="/girls" className="nav-link">GIRLS</Link>
          <Link to="/accessories" className="nav-link">ACCESSORIES</Link>
        </div>
      </div>

      {/* Page Title */}
      <div className="page-title">
        <h1>Women's T-Shirts</h1>
        <p>Discover our collection of {products.length} stylish women's t-shirts</p>
      </div>

      {/* Products Grid */}
      <div className="products-container">
        <div className="products-grid">
          {products.map((product) => (
            <div 
              key={product.id} 
              className="product-card"
              onClick={() => handleProductClick(product)}
            >
              <div className="product-image-container">
                {product.discount > 0 && <div className="sale-badge">SALE</div>}
                <img 
                  src={product.image || '/placeholder-image.jpg'} 
                  alt={product.name} 
                  className="product-image"
                />
              </div>
              <div className="product-info">
                <h3 className="product-name">{product.name}</h3>
                <div className="product-price">
                  <span className="current-price">${product.salePrice}</span>
                  {product.originalPrice && product.originalPrice !== product.salePrice && (
                    <span className="original-price">${product.originalPrice}</span>
                  )}
                  {product.discount > 0 && (
                    <span className="discount">-{product.discount}%</span>
                  )}
                </div>
                <div className="product-colors">
                  {product.colors && product.colors.slice(0, 3).map((color, index) => (
                    <span 
                      key={index} 
                      className="color-dot" 
                      style={{ backgroundColor: color.toLowerCase() }}
                      title={color}
                    ></span>
                  ))}
                  {product.colors && product.colors.length > 3 && (
                    <span className="more-colors">+{product.colors.length - 3}</span>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default Women_T_shirt;
