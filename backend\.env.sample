# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_NAME=clothing_store
DB_USER=root
DB_PASSWORD=your_database_password

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here_make_it_long_and_random
JWT_EXPIRES_IN=7d

# Server Configuration
PORT=3001
NODE_ENV=development

# CORS Configuration
FRONTEND_URL=http://localhost:3000

# Cloudinary Configuration (for image hosting)
CLOUDINARY_CLOUD_NAME=your_cloudinary_cloud_name
CLOUDINARY_API_KEY=your_cloudinary_api_key
CLOUDINARY_API_SECRET=your_cloudinary_api_secret

# Email Configuration (optional - for order confirmations)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password

# Payment Gateway Configuration (optional)
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key

# Session Configuration
SESSION_SECRET=your_session_secret_key_here

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# File Upload Configuration
MAX_FILE_SIZE=5242880
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,webp

# Security Configuration
BCRYPT_ROUNDS=10
COOKIE_SECURE=false
COOKIE_HTTP_ONLY=true
COOKIE_SAME_SITE=lax

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=logs/app.log

# Cache Configuration (optional)
REDIS_URL=redis://localhost:6379
CACHE_TTL=3600

# API Configuration
API_VERSION=v1
API_PREFIX=/api

# Development Configuration
DEBUG=true
ENABLE_SWAGGER=true
