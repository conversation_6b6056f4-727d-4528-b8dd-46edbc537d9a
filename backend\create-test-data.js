require('dotenv').config();
const { connectDB } = require('./config/database');
const { Category, Product, User } = require('./models');
const bcrypt = require('bcryptjs');

const createTestData = async () => {
  try {
    await connectDB();
    console.log('✅ Database connected');

    // Find or create test user
    let testUser = await User.findOne({ where: { email: '<EMAIL>' } });
    if (!testUser) {
      const hashedPassword = await bcrypt.hash('password123', 10);
      testUser = await User.create({
        name: 'Test User',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'user',
        isActive: true
      });
      console.log('✅ Test user created');
    } else {
      console.log('✅ Test user already exists');
    }

    // Find existing categories
    let womenCategory = await Category.findOne({ where: { name: 'Women' } });
    if (!womenCategory) {
      womenCategory = await Category.create({
        name: 'Women',
        description: 'Women clothing',
        slug: 'women',
        isActive: true
      });
    }

    let tshirtSubcategory = await Category.findOne({ where: { name: 'Women T-shirt' } });
    if (!tshirtSubcategory) {
      tshirtSubcategory = await Category.create({
        name: 'Women T-shirt',
        description: 'Women T-shirts',
        slug: 'women-t-shirt',
        parentId: womenCategory.id,
        isActive: true
      });
    }

    console.log('✅ Categories ready');

    // Create test products
    const products = [
      {
        name: 'Classic Cotton T-Shirt',
        description: 'Comfortable cotton t-shirt perfect for everyday wear',
        originalPrice: 29.99,
        salePrice: 24.99,
        discount: 17,
        categoryId: womenCategory.id,
        subcategoryId: tshirtSubcategory.id,
        images: JSON.stringify(['women_tshirt1.jpg']),
        colors: JSON.stringify(['black', 'white', 'red']),
        sizes: JSON.stringify(['XS', 'S', 'M', 'L', 'XL']),
        stock: 50,
        sku: 'WT001',
        brand: 'StyleCo',
        material: 'Cotton',
        tags: JSON.stringify(['casual', 'comfortable', 'basic']),
        ratingAverage: 4.5,
        ratingCount: 25,
        isActive: true,
        isFeatured: true,
        slug: 'classic-cotton-t-shirt'
      },
      {
        name: 'Premium V-Neck Tee',
        description: 'Soft premium cotton v-neck t-shirt with modern fit',
        originalPrice: 39.99,
        salePrice: 34.99,
        discount: 13,
        categoryId: womenCategory.id,
        subcategoryId: tshirtSubcategory.id,
        images: JSON.stringify(['women_tshirt2.jpg']),
        colors: JSON.stringify(['navy', 'gray', 'pink']),
        sizes: JSON.stringify(['XS', 'S', 'M', 'L', 'XL']),
        stock: 30,
        sku: 'WT002',
        brand: 'StyleCo',
        material: 'Premium Cotton',
        tags: JSON.stringify(['premium', 'v-neck', 'modern']),
        ratingAverage: 4.7,
        ratingCount: 18,
        isActive: true,
        isFeatured: false,
        slug: 'premium-v-neck-tee'
      },
      {
        name: 'Vintage Graphic Tee',
        description: 'Trendy vintage-style graphic t-shirt with unique design',
        originalPrice: 34.99,
        salePrice: 27.99,
        discount: 20,
        categoryId: womenCategory.id,
        subcategoryId: tshirtSubcategory.id,
        images: JSON.stringify(['women_tshirt3.jpg']),
        colors: JSON.stringify(['white', 'cream', 'light blue']),
        sizes: JSON.stringify(['S', 'M', 'L', 'XL']),
        stock: 25,
        sku: 'WT003',
        brand: 'VintageStyle',
        material: 'Cotton Blend',
        tags: JSON.stringify(['vintage', 'graphic', 'trendy']),
        ratingAverage: 4.3,
        ratingCount: 12,
        isActive: true,
        isFeatured: true,
        slug: 'vintage-graphic-tee'
      }
    ];

    await Product.bulkCreate(products);
    console.log('✅ Test products created');

    console.log('\n🎉 Test data created successfully!');
    console.log('Test user: <EMAIL> / password123');
    console.log('Products created: 3');
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error creating test data:', error);
    process.exit(1);
  }
};

createTestData();
