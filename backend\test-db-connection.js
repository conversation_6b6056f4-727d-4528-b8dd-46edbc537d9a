// Test Database Connection
// File: backend/test-db-connection.js

require('dotenv').config();
const mysql = require('mysql2/promise');

const testConnection = async () => {
  console.log('🔄 Testing database connection...');
  console.log(`Host: ${process.env.DB_HOST}`);
  console.log(`Port: ${process.env.DB_PORT}`);
  console.log(`Database: ${process.env.DB_NAME}`);
  console.log(`User: ${process.env.DB_USER}`);
  console.log('Password: [HIDDEN]');
  console.log('');

  try {
    // Test 1: Basic connection
    console.log('📡 Attempting basic connection...');
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST,
      port: parseInt(process.env.DB_PORT),
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
      connectTimeout: 20000, // 20 seconds
      acquireTimeout: 20000,
      timeout: 20000,
      ssl: {
        rejectUnauthorized: false
      }
    });

    console.log('✅ Database connected successfully!');

    // Test 2: Simple query
    console.log('🔍 Testing query execution...');
    const [rows] = await connection.execute('SELECT 1 + 1 AS result');
    console.log(`✅ Query test successful: ${rows[0].result}`);

    // Test 3: Check if tables exist
    console.log('📋 Checking existing tables...');
    const [tables] = await connection.execute('SHOW TABLES');
    console.log(`✅ Found ${tables.length} tables:`);
    tables.forEach(table => {
      console.log(`   - ${Object.values(table)[0]}`);
    });

    // Test 4: Check products if table exists
    if (tables.some(table => Object.values(table)[0] === 'products')) {
      const [products] = await connection.execute('SELECT COUNT(*) as count FROM products');
      console.log(`✅ Products in database: ${products[0].count}`);
    }

    await connection.end();
    console.log('✅ Connection closed successfully');
    
  } catch (error) {
    console.error('❌ Database connection failed:');
    console.error(`Error Code: ${error.code}`);
    console.error(`Error Message: ${error.message}`);
    
    if (error.code === 'ETIMEDOUT') {
      console.error('');
      console.error('🔧 Troubleshooting tips for timeout:');
      console.error('1. Check your internet connection');
      console.error('2. Verify Aiven database is running');
      console.error('3. Check firewall settings');
      console.error('4. Try connecting from different network');
    }
    
    if (error.code === 'EACCES' || error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.error('');
      console.error('🔧 Troubleshooting tips for access denied:');
      console.error('1. Verify database credentials');
      console.error('2. Check user permissions in Aiven console');
      console.error('3. Ensure IP is whitelisted (if applicable)');
    }
  }
};

// Test with different connection configurations
const testAlternativeConnection = async () => {
  console.log('\n🔄 Testing alternative connection method...');
  
  try {
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST,
      port: parseInt(process.env.DB_PORT),
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
      connectTimeout: 30000, // Increased timeout
      acquireTimeout: 30000,
      timeout: 30000,
      ssl: {
        rejectUnauthorized: false,
        requireSSL: true
      },
      flags: ['-FOUND_ROWS']
    });

    console.log('✅ Alternative connection successful!');
    await connection.end();
    
  } catch (error) {
    console.error('❌ Alternative connection also failed:', error.message);
  }
};

// Run tests
const runAllTests = async () => {
  await testConnection();
  await testAlternativeConnection();
  
  console.log('\n📊 Connection Test Summary:');
  console.log('If both tests failed, please check:');
  console.log('1. Internet connectivity');
  console.log('2. Aiven database status');
  console.log('3. Environment variables');
  console.log('4. Firewall/antivirus settings');
};

runAllTests();
