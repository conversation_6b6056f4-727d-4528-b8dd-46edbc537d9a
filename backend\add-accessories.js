// Add missing accessories products
require('dotenv').config();
const { connectDB } = require('./config/database');

// Product generation helpers
const generateRandomPrice = (min = 15, max = 150) => {
  return parseFloat((Math.random() * (max - min) + min).toFixed(2));
};

const getRandomColors = () => {
  const allColors = ['black', 'white', 'red', 'blue', 'green', 'yellow', 'pink', 'purple', 'orange', 'brown', 'gray', 'navy', 'beige', 'maroon', 'teal'];
  const count = Math.floor(Math.random() * 4) + 1;
  const shuffled = allColors.sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
};

const getRandomSizes = () => {
  const accessorySizes = ['One Size', 'S', 'M', 'L'];
  return accessorySizes.slice(0, Math.floor(Math.random() * 3) + 1);
};

const generateProductName = (subcategory, index) => {
  const prefixes = ['Classic', 'Modern', 'Vintage', 'Premium', 'Casual', 'Formal', 'Stylish', 'Comfortable', 'Trendy', 'Elegant'];
  const materials = ['Leather', 'Metal', 'Plastic', 'Canvas', 'Fabric', 'Synthetic'];
  const styles = ['Slim', 'Regular', 'Oversized', 'Compact', 'Deluxe', 'Standard'];
  
  const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];
  const material = materials[Math.floor(Math.random() * materials.length)];
  const style = styles[Math.floor(Math.random() * styles.length)];
  
  return `${prefix} ${style} ${material} ${subcategory} ${index + 1}`;
};

const generateDescription = (subcategory) => {
  const descriptions = [
    `High-quality ${subcategory.toLowerCase()} perfect for everyday use. Made with premium materials for durability and style.`,
    `Stylish ${subcategory.toLowerCase()} that combines fashion and functionality. Ideal for both casual and formal occasions.`,
    `Comfortable ${subcategory.toLowerCase()} designed with modern trends in mind. Features excellent craftsmanship and attention to detail.`,
    `Premium ${subcategory.toLowerCase()} offering superior quality and style. Perfect addition to your accessory collection.`,
    `Versatile ${subcategory.toLowerCase()} suitable for various occasions. Combines practicality with contemporary design elements.`
  ];
  return descriptions[Math.floor(Math.random() * descriptions.length)];
};

const generateImageName = (subcategory, index) => {
  const subcatLower = subcategory.toLowerCase().replace(/[^a-z]/g, '');
  return `${subcatLower}${(index % 20) + 1}.jpg`;
};

const accessoryCounts = {
  'Glasses': 19,
  'Watches': 6,
  'Gloves': 5,
  'Belt': 9,
  'Hat': 16,
  'Bag': 33,
  'Wallet': 8
};

async function addAccessories() {
  try {
    console.log('🔧 Adding missing accessories products...');
    await connectDB();
    
    const { Category, Product } = require('./models');
    
    // Get accessories category
    const accessoriesCategory = await Category.findOne({ where: { slug: 'accessories' } });
    if (!accessoriesCategory) {
      console.log('❌ Accessories category not found');
      return;
    }
    
    let totalAdded = 0;
    
    for (const [subcatName, count] of Object.entries(accessoryCounts)) {
      const subcategory = await Category.findOne({ where: { slug: subcatName.toLowerCase() } });
      
      if (!subcategory) {
        console.log(`❌ Subcategory not found: ${subcatName.toLowerCase()}`);
        continue;
      }
      
      // Check if products already exist
      const existingCount = await Product.count({ where: { subcategoryId: subcategory.id } });
      if (existingCount >= count) {
        console.log(`✅ ${subcatName}: Already has ${existingCount} products (need ${count})`);
        continue;
      }
      
      const toAdd = count - existingCount;
      console.log(`📦 Adding ${toAdd} products for ${subcatName}...`);
      
      for (let i = existingCount; i < count; i++) {
        const originalPrice = generateRandomPrice(20, 200);
        const discount = Math.floor(Math.random() * 50);
        const salePrice = parseFloat((originalPrice * (1 - discount / 100)).toFixed(2));
        
        await Product.create({
          name: generateProductName(subcatName, i),
          description: generateDescription(subcatName),
          originalPrice,
          salePrice,
          discount,
          categoryId: accessoriesCategory.id,
          subcategoryId: subcategory.id,
          images: JSON.stringify([generateImageName(subcatName, i)]),
          colors: JSON.stringify(getRandomColors()),
          sizes: JSON.stringify(getRandomSizes()),
          stock: Math.floor(Math.random() * 100) + 10,
          sku: `AC${subcatName.substring(0, 2).toUpperCase()}${String(i + 1).padStart(3, '0')}`,
          brand: 'StyleStore',
          material: ['Leather', 'Metal', 'Plastic', 'Canvas', 'Fabric'][Math.floor(Math.random() * 5)],
          tags: JSON.stringify([subcatName.toLowerCase(), 'accessories', 'fashion']),
          ratingAverage: parseFloat((Math.random() * 2 + 3).toFixed(1)),
          ratingCount: Math.floor(Math.random() * 100),
          isActive: true,
          isFeatured: Math.random() < 0.1,
          slug: `${generateProductName(subcatName, i).toLowerCase().replace(/\s+/g, '-')}-${i + 1}`
        });
        
        totalAdded++;
      }
      
      console.log(`✅ Added ${toAdd} products for ${subcatName}`);
    }
    
    console.log(`\n🎉 Added ${totalAdded} accessories products!`);
    
    // Verify final counts
    console.log('\n📋 Final verification:');
    for (const [subcatName, expectedCount] of Object.entries(accessoryCounts)) {
      const subcategory = await Category.findOne({ where: { slug: subcatName.toLowerCase() } });
      if (subcategory) {
        const actualCount = await Product.count({ where: { subcategoryId: subcategory.id } });
        console.log(`  ${subcatName}: ${actualCount}/${expectedCount} ${actualCount === expectedCount ? '✅' : '❌'}`);
      }
    }
    
  } catch (error) {
    console.error('❌ Adding accessories failed:', error.message);
  } finally {
    process.exit(0);
  }
}

addAccessories();
