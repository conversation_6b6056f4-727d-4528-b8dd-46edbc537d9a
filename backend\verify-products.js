// Verify product counts by category and subcategory
require('dotenv').config();
const { connectDB } = require('./config/database');

const expectedCounts = {
  'Women': {
    'T-shirt': 31,
    'Shirt': 43,
    'Jeans': 16,
    'Trouser': 17,
    'Jacket': 20,
    'Skirt': 17,
    'Shorts': 19,
    'Dress': 23,
    'Shoes': 20
  },
  'Men': {
    'T-shirt': 24,
    'Shirt': 22,
    'Jeans': 10,
    'Trouser': 9,
    'Jacket': 19,
    'Shoes': 17
  },
  'Boys': {
    'Clothing': 45,
    'Shoes': 17
  },
  'Girls': {
    'Clothing': 46,
    'Shoes': 20
  },
  'Accessories': {
    'Glasses': 19,
    'Watches': 6,
    'Gloves': 5,
    'Belt': 9,
    'Hat': 16,
    'Bag': 33,
    'Wallet': 8
  }
};

async function verifyProducts() {
  try {
    console.log('🔍 Verifying product counts...');
    await connectDB();
    
    const { Category, Product } = require('./models');
    
    let totalExpected = 0;
    let totalActual = 0;
    let allCorrect = true;
    
    console.log('\n📋 Product count verification:');
    
    for (const [categoryName, subcats] of Object.entries(expectedCounts)) {
      console.log(`\n${categoryName}:`);
      
      for (const [subcatName, expectedCount] of Object.entries(subcats)) {
        totalExpected += expectedCount;
        
        let subcategorySlug;
        if (categoryName === 'Accessories') {
          subcategorySlug = subcatName.toLowerCase();
        } else {
          subcategorySlug = `${categoryName.toLowerCase()}-${subcatName.toLowerCase()}`;
        }
        const subcategory = await Category.findOne({ where: { slug: subcategorySlug } });
        
        if (subcategory) {
          const actualCount = await Product.count({ where: { subcategoryId: subcategory.id } });
          totalActual += actualCount;
          
          const isCorrect = actualCount === expectedCount;
          if (!isCorrect) allCorrect = false;
          
          console.log(`  ${subcatName}: ${actualCount}/${expectedCount} ${isCorrect ? '✅' : '❌'}`);
        } else {
          console.log(`  ${subcatName}: ❌ Subcategory not found (${subcategorySlug})`);
          allCorrect = false;
        }
      }
    }
    
    console.log(`\n📊 Summary:`);
    console.log(`- Total expected: ${totalExpected}`);
    console.log(`- Total actual: ${totalActual}`);
    console.log(`- Status: ${allCorrect ? '✅ All counts correct!' : '❌ Some counts incorrect'}`);
    
    // Show sample products
    console.log('\n📦 Sample products:');
    const sampleProducts = await Product.findAll({
      limit: 5,
      include: [
        {
          model: Category,
          as: 'category',
          attributes: ['name']
        },
        {
          model: Category,
          as: 'subcategory',
          attributes: ['name']
        }
      ]
    });
    
    sampleProducts.forEach(product => {
      console.log(`  - ${product.name} (${product.category?.name} > ${product.subcategory?.name}) - $${product.salePrice}`);
    });
    
  } catch (error) {
    console.error('❌ Verification failed:', error.message);
  } finally {
    process.exit(0);
  }
}

verifyProducts();
