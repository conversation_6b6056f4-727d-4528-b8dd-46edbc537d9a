// 🌱 Database Seeder with Image-Free Product Data
// File: backend/seeders/clothingStoreSeeder.js

const { sequelize } = require('../config/database');
const { Category, Product, User } = require('../models');
const bcrypt = require('bcryptjs');

const seedClothingStore = async () => {
  try {
    console.log('🌱 Starting clothing store database seeding...');

    // Clear existing data
    await sequelize.sync({ force: true });
    console.log('📊 Database tables created/reset');

    // ================================
    // 🏪 SEED CATEGORIES
    // ================================
    console.log('📂 Seeding categories...');
    
    const categories = await Category.bulkCreate([
      {
        name: 'Women',
        slug: 'women',
        description: 'Women\'s clothing and accessories'
      },
      {
        name: 'Men', 
        slug: 'men',
        description: 'Men\'s clothing and accessories'
      },
      {
        name: 'Accessories',
        slug: 'accessories', 
        description: 'Fashion accessories for all'
      }
    ]);

    console.log(`✅ Created ${categories.length} categories`);

    // ================================
    // 👤 SEED USERS
    // ================================
    console.log('👤 Seeding users...');
    
    const hashedPassword = await bcrypt.hash('password123', 10);
    
    const users = await User.bulkCreate([
      {
        name: 'Admin User',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'admin'
      },
      {
        name: 'Test Customer',
        email: '<EMAIL>', 
        password: hashedPassword,
        role: 'customer'
      },
      {
        name: 'Jane Smith',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'customer'
      }
    ]);

    console.log(`✅ Created ${users.length} users`);

    // ================================
    // 👕 SEED PRODUCTS (NO IMAGE PATHS)
    // ================================
    console.log('👕 Seeding products...');

    const womenCategoryId = categories.find(c => c.slug === 'women').id;
    const menCategoryId = categories.find(c => c.slug === 'men').id;
    const accessoriesCategoryId = categories.find(c => c.slug === 'accessories').id;

    const products = await Product.bulkCreate([
      // ================================
      // 👗 WOMEN'S PRODUCTS
      // ================================
      {
        name: 'Regular Fitted Crop T-Shirt',
        description: 'A comfortable and stylish fitted crop t-shirt perfect for casual wear. Made from high-quality cotton blend fabric.',
        sku: 'WTS001',
        slug: 'regular-fitted-crop-t-shirt',
        originalPrice: 39.99,
        salePrice: 27.99,
        discountPercentage: 30,
        colors: JSON.stringify(['white', 'red']),
        sizes: JSON.stringify(['XS', 'S', 'M', 'L', 'XL']),
        material: 'Cotton Blend',
        brand: 'StyleStore',
        tags: JSON.stringify(['casual', 'crop', 'fitted']),
        stock: 50,
        categoryId: womenCategoryId,
        subcategory: 't-shirt',
        gender: 'women',
        isFeatured: true,
        metaTitle: 'Women\'s Fitted Crop T-Shirt - StyleStore',
        metaDescription: 'Comfortable fitted crop t-shirt for women in multiple colors and sizes.'
      },
      {
        name: 'Regular Crop Textured T-Shirt',
        description: 'Textured crop t-shirt with a modern fit and comfortable feel. Perfect for layering or wearing alone.',
        sku: 'WTS002',
        slug: 'regular-crop-textured-t-shirt',
        originalPrice: 44.99,
        salePrice: 31.49,
        discountPercentage: 30,
        colors: JSON.stringify(['pink']),
        sizes: JSON.stringify(['XS', 'S', 'M', 'L', 'XL']),
        material: 'Cotton',
        brand: 'StyleStore',
        tags: JSON.stringify(['casual', 'textured', 'crop']),
        stock: 35,
        categoryId: womenCategoryId,
        subcategory: 't-shirt',
        gender: 'women',
        isFeatured: false
      },
      {
        name: 'Women\'s Cream Blouse',
        description: 'Elegant cream blouse perfect for office or casual wear. Features a classic cut with modern styling.',
        sku: 'WB001',
        slug: 'womens-cream-blouse',
        originalPrice: 33.99,
        salePrice: 23.99,
        discountPercentage: 29,
        colors: JSON.stringify(['cream', 'white']),
        sizes: JSON.stringify(['XS', 'S', 'M', 'L']),
        material: 'Polyester Blend',
        brand: 'StyleStore',
        tags: JSON.stringify(['elegant', 'office', 'blouse']),
        stock: 25,
        categoryId: womenCategoryId,
        subcategory: 'shirt',
        gender: 'women',
        isFeatured: true
      },
      {
        name: 'Classic V-Neck T-Shirt',
        description: 'Timeless v-neck t-shirt that goes with everything. Soft and breathable fabric.',
        sku: 'WTS003',
        slug: 'classic-v-neck-t-shirt',
        originalPrice: 49.99,
        salePrice: 34.99,
        discountPercentage: 30,
        colors: JSON.stringify(['beige', 'white', 'navy']),
        sizes: JSON.stringify(['XS', 'S', 'M', 'L', 'XL']),
        material: 'Cotton',
        brand: 'StyleStore',
        tags: JSON.stringify(['classic', 'v-neck', 'versatile']),
        stock: 40,
        categoryId: womenCategoryId,
        subcategory: 't-shirt',
        gender: 'women',
        isFeatured: true
      },

      // ================================
      // 👗 WOMEN'S DRESSES
      // ================================
      {
        name: 'Floral Ruffle Dress',
        description: 'Beautiful floral dress with ruffle details, perfect for summer occasions and casual outings.',
        sku: 'WD001',
        slug: 'floral-ruffle-dress',
        originalPrice: 59.99,
        salePrice: 17.99,
        discountPercentage: 70,
        colors: JSON.stringify(['pink', 'white']),
        sizes: JSON.stringify(['XS', 'S', 'M', 'L', 'XL']),
        material: 'Chiffon',
        brand: 'StyleStore',
        tags: JSON.stringify(['floral', 'summer', 'ruffle']),
        stock: 30,
        categoryId: womenCategoryId,
        subcategory: 'dress',
        gender: 'women',
        isFeatured: true
      },
      {
        name: 'Lace Midi Dress',
        description: 'Sophisticated lace midi dress perfect for special occasions and evening events.',
        sku: 'WD002',
        slug: 'lace-midi-dress',
        originalPrice: 64.99,
        salePrice: 19.49,
        discountPercentage: 70,
        colors: JSON.stringify(['red', 'black']),
        sizes: JSON.stringify(['XS', 'S', 'M', 'L']),
        material: 'Lace',
        brand: 'StyleStore',
        tags: JSON.stringify(['formal', 'lace', 'midi']),
        stock: 20,
        categoryId: womenCategoryId,
        subcategory: 'dress',
        gender: 'women',
        isFeatured: false
      },

      // ================================
      // 👔 MEN'S PRODUCTS
      // ================================
      {
        name: 'Casual Cotton Shirt',
        description: 'Comfortable casual cotton shirt perfect for everyday wear and weekend outings.',
        sku: 'MS001',
        slug: 'casual-cotton-shirt',
        originalPrice: 49.99,
        salePrice: 34.99,
        discountPercentage: 30,
        colors: JSON.stringify(['white', 'blue', 'gray']),
        sizes: JSON.stringify(['S', 'M', 'L', 'XL', 'XXL']),
        material: 'Cotton',
        brand: 'StyleStore',
        tags: JSON.stringify(['casual', 'cotton', 'comfortable']),
        stock: 40,
        categoryId: menCategoryId,
        subcategory: 'shirt',
        gender: 'men',
        isFeatured: true
      },
      {
        name: 'Formal Button-Down Shirt',
        description: 'Professional button-down shirt ideal for office wear and formal occasions.',
        sku: 'MS002',
        slug: 'formal-button-down-shirt',
        originalPrice: 59.99,
        salePrice: 39.99,
        discountPercentage: 33,
        colors: JSON.stringify(['white', 'light blue', 'navy']),
        sizes: JSON.stringify(['S', 'M', 'L', 'XL', 'XXL']),
        material: 'Cotton Blend',
        brand: 'StyleStore',
        tags: JSON.stringify(['formal', 'office', 'professional']),
        stock: 35,
        categoryId: menCategoryId,
        subcategory: 'shirt',
        gender: 'men',
        isFeatured: true
      },

      // ================================
      // 🎒 ACCESSORIES
      // ================================
      {
        name: 'Classic Leather Belt',
        description: 'Premium leather belt with classic buckle design. Perfect for both casual and formal wear.',
        sku: 'AB001',
        slug: 'classic-leather-belt',
        originalPrice: 29.99,
        salePrice: 19.99,
        discountPercentage: 33,
        colors: JSON.stringify(['black', 'brown']),
        sizes: JSON.stringify(['28', '30', '32', '34', '36', '38']),
        material: 'Genuine Leather',
        brand: 'StyleStore',
        tags: JSON.stringify(['leather', 'classic', 'belt']),
        stock: 60,
        categoryId: accessoriesCategoryId,
        subcategory: 'belt',
        gender: 'unisex',
        isFeatured: false
      },
      {
        name: 'Canvas Crossbody Bag',
        description: 'Stylish canvas crossbody bag perfect for daily use. Multiple compartments for organization.',
        sku: 'AB002',
        slug: 'canvas-crossbody-bag',
        originalPrice: 39.99,
        salePrice: 29.99,
        discountPercentage: 25,
        colors: JSON.stringify(['black', 'khaki', 'navy']),
        sizes: JSON.stringify(['One Size']),
        material: 'Canvas',
        brand: 'StyleStore',
        tags: JSON.stringify(['bag', 'crossbody', 'daily']),
        stock: 45,
        categoryId: accessoriesCategoryId,
        subcategory: 'bag',
        gender: 'unisex',
        isFeatured: false
      },

      // ================================
      // 📊 MORE SAMPLE PRODUCTS FOR TESTING
      // ================================
      {
        name: 'Summer Tank Top',
        description: 'Lightweight tank top perfect for hot summer days.',
        sku: 'WTS004',
        slug: 'summer-tank-top',
        originalPrice: 24.99,
        salePrice: 19.99,
        discountPercentage: 20,
        colors: JSON.stringify(['yellow', 'coral', 'mint']),
        sizes: JSON.stringify(['XS', 'S', 'M', 'L']),
        material: 'Cotton',
        brand: 'StyleStore',
        tags: JSON.stringify(['summer', 'tank', 'lightweight']),
        stock: 30,
        categoryId: womenCategoryId,
        subcategory: 't-shirt',
        gender: 'women',
        isFeatured: false
      },
      {
        name: 'Polo Shirt',
        description: 'Classic polo shirt suitable for both casual and semi-formal occasions.',
        sku: 'MS003',
        slug: 'polo-shirt',
        originalPrice: 44.99,
        salePrice: null, // No sale price
        discountPercentage: 0,
        colors: JSON.stringify(['navy', 'white', 'burgundy']),
        sizes: JSON.stringify(['S', 'M', 'L', 'XL']),
        material: 'Cotton Pique',
        brand: 'StyleStore',
        tags: JSON.stringify(['polo', 'classic', 'versatile']),
        stock: 25,
        categoryId: menCategoryId,
        subcategory: 'shirt',
        gender: 'men',
        isFeatured: false
      }
    ]);

    console.log(`✅ Created ${products.length} products`);

    // ================================
    // 📊 SUMMARY
    // ================================
    console.log('\n🎉 Database seeding completed successfully!');
    console.log('📊 Summary:');
    console.log(`   Categories: ${categories.length}`);
    console.log(`   Users: ${users.length}`);
    console.log(`   Products: ${products.length}`);
    console.log('\n🔗 Test the API endpoints:');
    console.log('   GET /api/products - All products');
    console.log('   GET /api/products/featured - Featured products');
    console.log('   GET /api/products?category=women - Women\'s products');
    console.log('   GET /api/products?gender=women&subcategory=t-shirt - Women\'s t-shirts');
    console.log('\n👤 Test users:');
    console.log('   Admin: <EMAIL> / password123');
    console.log('   Customer: <EMAIL> / password123');

    return {
      categories: categories.length,
      users: users.length,
      products: products.length
    };

  } catch (error) {
    console.error('❌ Error seeding database:', error);
    throw error;
  }
};

// Run seeder if called directly
if (require.main === module) {
  seedClothingStore()
    .then((result) => {
      console.log('✅ Seeding completed:', result);
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Seeding failed:', error);
      process.exit(1);
    });
}

module.exports = { seedClothingStore };
