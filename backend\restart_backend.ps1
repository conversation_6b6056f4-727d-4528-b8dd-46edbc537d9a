# Backend Restart Script
# Save as: restart_backend.ps1

Write-Host "🔄 RESTARTING BACKEND SERVER" -ForegroundColor Cyan
Write-Host "============================" -ForegroundColor Cyan

# Kill existing Node.js processes on port 3001
Write-Host "`n1. Stopping existing backend processes..." -ForegroundColor Yellow
$port3001Process = netstat -ano | Select-String ":3001" | ForEach-Object {
    $fields = $_ -split '\s+'
    $pid = $fields[-1]
    if ($pid -and $pid -ne "0") {
        Write-Host "Killing process on port 3001 (PID: $pid)" -ForegroundColor Red
        Stop-Process -Id $pid -Force -ErrorAction SilentlyContinue
    }
}

# Wait a moment
Start-Sleep -Seconds 2

# Navigate to backend directory
Write-Host "`n2. Navigating to backend directory..." -ForegroundColor Yellow
Set-Location "d:\Back-end\PROJECT_BACKEND\backend"

# Check if package.json exists
if (Test-Path "package.json") {
    Write-Host "✅ Found package.json" -ForegroundColor Green
} else {
    Write-Host "❌ package.json not found! Check directory path." -ForegroundColor Red
    exit 1
}

# Check if .env file exists
if (Test-Path ".env") {
    Write-Host "✅ Found .env file" -ForegroundColor Green
} else {
    Write-Host "⚠️ .env file not found! Creating basic .env..." -ForegroundColor Yellow
    @"
NODE_ENV=development
PORT=3001
DB_HOST=mysql-17f276b8-chuthomey3-12ec.e.aivencloud.com
DB_PORT=10392
DB_NAME=clothes
DB_USER=avnadmin
DB_PASSWORD=AVNS_YLX_gg3pyvyy7t_F78G
JWT_SECRET=your_jwt_secret_key_here
CORS_ORIGIN=http://localhost:3000
"@ | Out-File -FilePath ".env" -Encoding utf8
}

# Install dependencies if needed
Write-Host "`n3. Checking dependencies..." -ForegroundColor Yellow
if (!(Test-Path "node_modules")) {
    Write-Host "Installing dependencies..." -ForegroundColor Yellow
    npm install
}

# Start backend server
Write-Host "`n4. Starting backend server..." -ForegroundColor Yellow
Write-Host "Backend will start on port 3001..." -ForegroundColor Green
Write-Host "Press Ctrl+C to stop the server when needed." -ForegroundColor Yellow

# Start server and capture output
npm start
