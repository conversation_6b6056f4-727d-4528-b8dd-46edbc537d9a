// Test seed file to verify database connection
require('dotenv').config();
const { connectDB, sequelize } = require('./config/database');
const { User, Category, Product } = require('./models');
const bcrypt = require('bcryptjs');

async function testSeed() {
  try {
    console.log('🧪 Testing database connection and seeding...');
    
    // Connect to database
    await connectDB();
    console.log('✅ Database connected');
    
    // Test sync
    await sequelize.sync({ force: true });
    console.log('✅ Database tables synchronized');
    
    // Create admin user
    const hashedPassword = await bcrypt.hash('admin123', 10);
    const admin = await User.create({
      name: 'Admin User',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'admin'
    });
    console.log('✅ Admin user created:', admin.id);
    
    // Create a test category
    const category = await Category.create({
      name: 'Women',
      description: 'Women clothing and accessories',
      slug: 'women',
      isActive: true
    });
    console.log('✅ Test category created:', category.id);
    
    // Create a test subcategory
    const subcategory = await Category.create({
      name: 'Women T-shirt',
      description: 'T-shirts for women',
      parentId: category.id,
      slug: 'women-t-shirt',
      isActive: true
    });
    console.log('✅ Test subcategory created:', subcategory.id);
    
    // Create a test product
    const product = await Product.create({
      name: 'Test T-Shirt',
      description: 'A test t-shirt product',
      originalPrice: 29.99,
      salePrice: 19.99,
      discount: 33,
      categoryId: category.id,
      subcategoryId: subcategory.id,
      images: JSON.stringify(['women_tshirt1.jpg']),
      colors: JSON.stringify(['red', 'blue']),
      sizes: JSON.stringify(['S', 'M', 'L']),
      stock: 50,
      sku: 'WO001',
      brand: 'StyleStore',
      material: 'Cotton',
      tags: JSON.stringify(['t-shirt', 'women', 'fashion']),
      ratingAverage: 4.5,
      ratingCount: 10,
      isActive: true,
      isFeatured: false,
      slug: 'test-t-shirt'
    });
    console.log('✅ Test product created:', product.id);
    
    console.log('\n🎉 Test seeding completed successfully!');
    
  } catch (error) {
    console.error('❌ Test seeding failed:', error);
    console.error(error.stack);
  } finally {
    process.exit(0);
  }
}

testSeed();
