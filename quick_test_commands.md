# 🚀 Quick Payment Testing Commands
# File: quick_test_commands.md

## 🔧 Backend API Endpoints

### Authentication
```bash
# Login (get token)
POST http://localhost:3001/api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "your-password"
}
```

### Test Order Creation
```bash
# Create test order (requires auth token)
POST http://localhost:3001/api/test/create-test-order
Authorization: Bearer YOUR_TOKEN
```

### Process Test Payment
```bash
# Process payment (replace ORDER_ID)
POST http://localhost:3001/api/test/process-payment/ORDER_ID
Authorization: Bearer YOUR_TOKEN
```

### Get Payment Summary
```bash
# Get payment summary
GET http://localhost:3001/api/test/payment-summary
Authorization: Bearer YOUR_TOKEN
```

### Database Statistics
```bash
# Get database stats
GET http://localhost:3001/api/test/db-stats
Authorization: Bearer YOUR_TOKEN
```

### Cleanup Test Data
```bash
# Clean up test data
DELETE http://localhost:3001/api/test/cleanup-test-data
Authorization: Bearer YOUR_TOKEN
```

## 🗄️ Database Commands (Quick Check)

### Show All Tables
```sql
SHOW TABLES;
```

### Check Recent Payments
```sql
SELECT * FROM payments ORDER BY createdAt DESC LIMIT 5;
```

### Check Recent Orders
```sql
SELECT * FROM orders ORDER BY createdAt DESC LIMIT 5;
```

### Payment Status Summary
```sql
SELECT status, COUNT(*) as count, SUM(amount) as total 
FROM payments GROUP BY status;
```

### Order Status Summary
```sql
SELECT orderStatus, paymentStatus, COUNT(*) as count 
FROM orders GROUP BY orderStatus, paymentStatus;
```

### Check Test Data
```sql
-- Test orders
SELECT * FROM orders WHERE orderNumber LIKE 'ORD-TEST-%';

-- Test payments
SELECT * FROM payments WHERE transactionId LIKE 'TXN-TEST-%';
```

## 🛠️ PowerShell Testing

### Run Full Test Suite
```powershell
# Navigate to project directory
cd "d:\Back-end\PROJECT_BACKEND"

# Run payment API test script
.\test_payment_api.ps1
```

### Manual cURL Commands
```bash
# Health check
curl http://localhost:3001/api/health

# Login
curl -X POST http://localhost:3001/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password"}'

# Create test order (replace TOKEN)
curl -X POST http://localhost:3001/api/test/create-test-order \
  -H "Authorization: Bearer TOKEN"

# Process payment (replace TOKEN and ORDER_ID)
curl -X POST http://localhost:3001/api/test/process-payment/ORDER_ID \
  -H "Authorization: Bearer TOKEN"
```

## 📊 Database Verification

### Check Payment Flow
```sql
-- Complete payment verification
SELECT 
    p.transactionId,
    p.amount,
    p.status as payment_status,
    o.orderNumber,
    o.orderStatus,
    u.email as customer
FROM payments p
JOIN orders o ON p.orderId = o.id
JOIN users u ON o.userId = u.id
ORDER BY p.createdAt DESC
LIMIT 10;
```

### Stock Verification
```sql
-- Check product stock changes
SELECT 
    pr.name,
    pr.stock as current_stock,
    SUM(oi.quantity) as total_sold
FROM products pr
LEFT JOIN order_items oi ON pr.id = oi.productId
LEFT JOIN orders o ON oi.orderId = o.id
WHERE o.paymentStatus = 'paid'
GROUP BY pr.id, pr.name, pr.stock;
```

## 🧪 Testing Scenarios

### Scenario 1: New Customer Purchase
1. Register new user
2. Login and get token
3. Create test order
4. Process payment
5. Verify database changes

### Scenario 2: Multiple Orders
1. Create multiple test orders
2. Process payments for each
3. Check payment summary
4. Verify stock updates

### Scenario 3: Payment Validation
1. Create order
2. Process payment
3. Check payment status
4. Verify order completion
5. Confirm stock reduction

## 🔍 Troubleshooting

### Common Issues
- **401 Unauthorized**: Check if token is valid and properly set
- **404 Not Found**: Verify API endpoints and route configuration
- **500 Server Error**: Check server logs and database connection
- **CORS Error**: Ensure CORS is properly configured in server.js

### Debug Commands
```bash
# Check if backend is running
curl http://localhost:3001/api/health

# Check specific user orders
curl -H "Authorization: Bearer TOKEN" \
  http://localhost:3001/api/orders

# Check payment routes
curl -H "Authorization: Bearer TOKEN" \
  http://localhost:3001/api/test/db-stats
```

## ⚡ Quick Start

1. **Start Backend**: `cd backend && npm start`
2. **Start Frontend**: `cd frontend && npm run dev`
3. **Run Tests**: `.\test_payment_api.ps1`
4. **Check Database**: Use SQL commands from `payment_database_commands.sql`
5. **Verify API**: Use endpoints from this reference guide
