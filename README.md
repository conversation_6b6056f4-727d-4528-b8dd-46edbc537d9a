# StyleStore - Full-Stack E-commerce Clothing Store

A complete full-stack e-commerce clothing store built with React.js frontend and Express.js + MySQL backend.

## 🚀 Features

### Frontend (React.js)
- **Product Catalog**: Browse products by categories and subcategories
- **User Authentication**: Register, login, and user profile management
- **Shopping Cart**: Add products to cart with quantity selection
- **Payment System**: Complete checkout process with order confirmation
- **Order History**: View past orders with detailed information
- **Responsive Design**: Mobile-friendly UI design
- **Real-time Updates**: Dynamic product data from backend API

### Backend (Express.js + MySQL)
- **RESTful API**: Complete API for all frontend operations
- **User Management**: Registration, authentication, profile management
- **Product Management**: CRUD operations for products and categories
- **Order Processing**: Handle orders, payments, and order history
- **Database Integration**: MySQL with Sequelize ORM
- **Security**: JWT authentication, password hashing, input validation
- **Error Handling**: Comprehensive error handling and logging

### Database Schema
- **Users**: User accounts with authentication
- **Categories**: Product categories and subcategories
- **Products**: Product catalog with images, prices, variants
- **Orders**: Order management with items and status tracking
- **Order Items**: Individual items within orders

## 📦 Project Structure

```
├── backend/
│   ├── config/          # Database and app configuration
│   ├── controllers/     # Route controllers
│   ├── middleware/      # Custom middleware
│   ├── models/          # Sequelize models
│   ├── routes/          # API routes
│   ├── seeders/         # Database seeders
│   └── server.js        # Express server entry point
├── frontend/
│   ├── public/          # Static files
│   ├── src/
│   │   ├── components/  # React components
│   │   ├── services/    # API services
│   │   ├── styles/      # CSS styles
│   │   └── App.js       # Main App component
└── README.md
```

## 🛠️ Installation & Setup

### Prerequisites
- Node.js (v14 or higher)
- MySQL (v8.0 or higher)
- npm or yarn package manager

### Backend Setup

1. **Navigate to backend directory**
   ```bash
   cd backend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Configuration**
   ```bash
   cp .env.sample .env
   ```
   Edit `.env` file with your database credentials and configuration.

4. **Database Setup**
   - Create a MySQL database named `clothing_store`
   - Update database credentials in `.env` file

5. **Run Database Migrations**
   ```bash
   npm run migrate
   ```

6. **Seed Database with Products**
   ```bash
   node seeders/complete-product-seed.js
   ```

7. **Start Backend Server**
   ```bash
   npm start
   ```
   Backend will run on `http://localhost:3001`

### Frontend Setup

1. **Navigate to frontend directory**
   ```bash
   cd frontend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Configuration**
   ```bash
   cp .env.sample .env
   ```
   Update API URL and other configurations as needed.

4. **Start Frontend Development Server**
   ```bash
   npm start
   ```
   Frontend will run on `http://localhost:3000`

## 📊 Database Schema

### Product Categories & Quantities
- **Women**: T-shirt (31), Shirt (43), Jacket (20), Skirt (17), Trouser (17), Shorts (19), Jeans (16), Dress (23), Shoes (20)
- **Men**: T-shirt (24), Shirt (22), Jeans (10), Jacket (19), Trouser (9), Shoes (17)
- **Boys**: Clothing (45), Shoes (17)
- **Girls**: Clothing (46), Shoes (20)
- **Accessories**: Glasses (19), Watches (6), Gloves (5), Belt (9), Hat (16), Bag (33), Wallet (8)

**Total Products**: 531 items across all categories

## 🔧 API Endpoints

### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `GET /api/auth/profile` - Get user profile

### Products
- `GET /api/products` - Get all products (with filtering)
- `GET /api/products/:id` - Get single product
- `GET /api/products?subcategory=women-t-shirt` - Filter by subcategory

### Orders
- `POST /api/payments/purchase` - Create new order
- `GET /api/orders` - Get user order history
- `GET /api/orders/:id` - Get single order details

### User Profile
- `GET /api/user-profile/profile` - Get user profile
- `PUT /api/user-profile/profile` - Update user profile
- `DELETE /api/user-profile/profile` - Delete user account

## 🎨 Frontend Components

### Main Components
- `Collection.jsx` - Product catalog page
- `PaymentPage.jsx` - Checkout and payment processing
- `OrderHistory.jsx` - User order history
- `UserProfile.jsx` - User profile management
- `Login.jsx` / `Register.jsx` - Authentication forms

### Subcategory Components
- `Women_T_shirt.jsx` - Women's t-shirt collection
- Similar components for other subcategories

## 🔐 Security Features

- JWT-based authentication
- Password hashing with bcrypt
- Input validation and sanitization
- CORS protection
- Rate limiting
- SQL injection prevention with Sequelize ORM

## 🚀 Deployment

### Backend Deployment
1. Set up production database
2. Update environment variables for production
3. Deploy to your preferred hosting service (Heroku, AWS, etc.)

### Frontend Deployment
1. Build production version: `npm run build`
2. Deploy build folder to static hosting (Netlify, Vercel, etc.)
3. Update API URLs for production

## 📝 Environment Variables

See `.env.sample` files in both `backend/` and `frontend/` directories for complete configuration options.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 📞 Support

For support and questions, please contact: <EMAIL>
