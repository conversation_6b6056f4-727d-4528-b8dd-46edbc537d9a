# Payment API Testing Script
# Save as: test_payment.ps1

Write-Host "💳 PAYMENT PROCESSING TEST" -ForegroundColor Cyan
Write-Host "=========================" -ForegroundColor Cyan

# Configuration
$baseUrl = "http://localhost:3001/api"
$token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."  # Replace with your actual JWT token
$orderId = 1  # Replace with actual order ID

# Test payment processing
Write-Host "`n🔄 Processing payment for Order ID: $orderId" -ForegroundColor Yellow

$paymentData = @{
    orderId = $orderId
    paymentMethod = "credit_card"
    amount = 89.97
    paymentGateway = "stripe"
    transactionId = "txn_test_" + (Get-Date).Ticks
    gatewayResponse = @{
        stripe_payment_intent = "pi_test_12345"
        status = "succeeded"
        charge_id = "ch_test_67890"
    }
} | ConvertTo-Json -Depth 3

$headers = @{
    "Content-Type" = "application/json"
    "Authorization" = "Bearer $token"
}

try {
    $response = Invoke-RestMethod -Uri "$baseUrl/payments" -Method POST -Body $paymentData -Headers $headers
    
    Write-Host "✅ Payment processed successfully!" -ForegroundColor Green
    Write-Host "Payment ID: $($response.data.payment.id)" -ForegroundColor Green
    Write-Host "Transaction ID: $($response.data.payment.transactionId)" -ForegroundColor Green
    Write-Host "Order Status: $($response.data.order.status)" -ForegroundColor Green
    Write-Host "Payment Status: $($response.data.order.paymentStatus)" -ForegroundColor Green
    
    # Show stock updates
    Write-Host "`n📦 Stock Updates:" -ForegroundColor Yellow
    foreach ($update in $response.data.stockUpdates) {
        Write-Host "- $($update.productName): $($update.previousStock) → $($update.newStock) (Sold: $($update.soldQuantity))" -ForegroundColor White
    }
    
} catch {
    Write-Host "❌ Payment failed!" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $errorResponse = $_.Exception.Response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($errorResponse)
        $errorBody = $reader.ReadToEnd()
        Write-Host "Response: $errorBody" -ForegroundColor Red
    }
}

Write-Host "`n=========================" -ForegroundColor Cyan
