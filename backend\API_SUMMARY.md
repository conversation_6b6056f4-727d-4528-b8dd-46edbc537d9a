# 📋 Backend API Endpoints Summary

## Total API Endpoints: **71 Routes**

### 🔐 **Authentication APIs (4 endpoints)**
**Base URL:** `/api/auth`

1. `POST /api/auth/register` - User registration
2. `POST /api/auth/login` - User login  
3. `GET /api/auth/me` - Get current user profile
4. `PUT /api/auth/profile` - Update user profile

---

### 👤 **User Management APIs (8 endpoints)**
**Base URL:** `/api/users`

**Favorites:**
5. `POST /api/users/favorites/:productId` - Add product to favorites
6. `DELETE /api/users/favorites/:productId` - Remove from favorites
7. `GET /api/users/favorites` - Get user favorites

**Cart Management:**
8. `POST /api/users/cart` - Add item to cart
9. `PUT /api/users/cart/:itemId` - Update cart item
10. `DELETE /api/users/cart/:itemId` - Remove cart item
11. `GET /api/users/cart` - Get user cart
12. `DELETE /api/users/cart` - Clear entire cart

---

### 👤 **User Profile APIs (3 endpoints)**
**Base URL:** `/api/user`

13. `GET /api/user/profile` - Get detailed user profile
14. `GET /api/user/verify/:userId` - Verify user existence
15. `PUT /api/user/profile` - Update user profile

---

### 🛍️ **Product APIs (8 endpoints)**
**Base URL:** `/api/products`

**Public Product Access:**
16. `GET /api/products` - Get all products (with filtering)
17. `GET /api/products/search` - Search products
18. `GET /api/products/featured` - Get featured products
19. `GET /api/products/:id` - Get single product details

**Product Reviews:**
20. `POST /api/products/:id/reviews` - Add product review (Auth required)

**Admin Product Management:**
21. `POST /api/products` - Create new product (Admin only)
22. `PUT /api/products/:id` - Update product (Admin only)
23. `DELETE /api/products/:id` - Delete product (Admin only)

---

### 📂 **Category APIs (5 endpoints)**
**Base URL:** `/api/categories`

**Public Category Access:**
24. `GET /api/categories` - Get all categories
25. `GET /api/categories/:id` - Get single category

**Admin Category Management:**
26. `POST /api/categories` - Create new category (Admin only)
27. `PUT /api/categories/:id` - Update category (Admin only)
28. `DELETE /api/categories/:id` - Delete category (Admin only)

---

### 🛒 **Cart APIs (5 endpoints)**
**Base URL:** `/api/cart`

29. `POST /api/cart` - Add item to cart
30. `GET /api/cart` - Get user cart
31. `PUT /api/cart/:id` - Update cart item
32. `DELETE /api/cart/:id` - Remove cart item
33. `DELETE /api/cart` - Clear entire cart

---

### 📦 **Order Management APIs (7 endpoints)**
**Base URL:** `/api/orders`

**User Order Management:**
34. `POST /api/orders` - Create new order
35. `GET /api/orders/history` - Get order history
36. `GET /api/orders/my-orders` - Get user orders
37. `GET /api/orders/:id` - Get specific order details
38. `PUT /api/orders/:id/cancel` - Cancel order

**Admin Order Management:**
39. `GET /api/orders` - Get all orders (Admin only)
40. `PUT /api/orders/:id/status` - Update order status (Admin only)

---

### ✅ **Order Verification APIs (4 endpoints)**
**Base URL:** `/api/verification`

41. `GET /api/verification/orders/:userId` - Get user orders for verification
42. `GET /api/verification/order/:orderId` - Get specific order verification
43. `GET /api/verification/payment/verify/:orderId` - Verify payment status
44. `GET /api/verification/recent/:userId` - Get recent orders

---

### 💳 **Payment APIs (6 endpoints)**
**Base URL:** `/api/payments`

45. `POST /api/payments/purchase` - Complete purchase with payment
46. `POST /api/payments` - Process payment
47. `GET /api/payments/:paymentId` - Get payment details
48. `GET /api/payments` - Get user payments
49. `POST /api/payments/:paymentId/refund` - Refund payment (Admin only)
50. `GET /api/payments/receipt/:orderId` - Generate receipt

---

### 🧪 **Testing APIs (13 endpoints)**
**Base URL:** `/api/test`

**Health & Connection:**
51. `GET /api/test/health` - Health check
52. `GET /api/test/db-connection` - Test database connection

**Product Testing:**
53. `GET /api/test/products/all` - Get all products for testing
54. `GET /api/test/products/category/:categoryName` - Test category filtering
55. `GET /api/test/products/featured` - Test featured products
56. `GET /api/test/products/sale` - Test sale products

**Database Testing:**
57. `GET /api/test/database-stats` - Get database statistics
58. `GET /api/test/db-stats` - Get detailed DB stats
59. `GET /api/test/run-all-tests` - Run all automated tests

**Order & Payment Testing:**
60. `POST /api/test/create-test-order` - Create test order
61. `POST /api/test/process-payment/:orderId` - Process test payment
62. `GET /api/test/payment-summary` - Get payment summary
63. `DELETE /api/test/cleanup-test-data` - Clean up test data

---

### 🔧 **Debug APIs (8 endpoints)** *(Development Only)*
**Base URL:** `/api/debug`

64. `GET /api/debug/db-test` - Debug database connection
65. `GET /api/debug/users` - Debug user data
66. `GET /api/debug/orders` - Debug order data
67. `GET /api/debug/order-items` - Debug order items
68. `GET /api/debug/stats` - Debug statistics
69. `GET /api/debug/setup-database` - Setup database for debugging
70. `GET /api/debug/raw-query/:query` - Execute raw SQL query
71. `GET /api/debug/payment-data` - Debug payment data

---

### 🌐 **Health Check API (1 endpoint)**
72. `GET /api/health` - Main application health check

---

## 📊 **API Summary by Category:**

| Category | Public | Auth Required | Admin Only | Total |
|----------|--------|---------------|------------|-------|
| Authentication | 2 | 2 | 0 | **4** |
| User Management | 0 | 8 | 0 | **8** |
| User Profile | 0 | 3 | 0 | **3** |
| Products | 4 | 1 | 3 | **8** |
| Categories | 2 | 0 | 3 | **5** |
| Cart | 0 | 5 | 0 | **5** |
| Orders | 0 | 5 | 2 | **7** |
| Order Verification | 0 | 4 | 0 | **4** |
| Payments | 0 | 5 | 1 | **6** |
| Testing | 13 | 0 | 0 | **13** |
| Debug (Dev) | 8 | 0 | 0 | **8** |
| Health Check | 1 | 0 | 0 | **1** |
| **TOTAL** | **30** | **33** | **9** | **72** |

---

## 🔒 **Authentication Requirements:**

- **Public APIs (30):** No authentication required
- **User APIs (33):** JWT token required
- **Admin APIs (9):** JWT token + admin role required

---

## 🚀 **Main API Features:**

✅ **User Management:** Registration, login, profile management
✅ **Product Catalog:** Browse, search, filter, reviews
✅ **Shopping Cart:** Add, update, remove items
✅ **Order Processing:** Create orders, payment processing
✅ **Payment Integration:** Multiple payment methods support
✅ **Order Tracking:** Order history, status updates
✅ **Admin Panel:** Product/category/order management
✅ **Testing Framework:** Comprehensive testing endpoints
✅ **Debug Tools:** Development debugging utilities

Your backend has **72 total API endpoints** covering all aspects of an e-commerce clothing store! 🎉
