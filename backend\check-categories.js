// Check all categories and subcategories
require('dotenv').config();
const { connectDB } = require('./config/database');

async function checkCategories() {
  try {
    console.log('🔍 Checking all categories and subcategories...');
    await connectDB();
    
    const { Category } = require('./models');
    
    // Get all main categories
    const mainCategories = await Category.findAll({
      where: { parentId: null },
      order: [['name', 'ASC']]
    });
    
    console.log('\n📋 Categories and Subcategories:');
    
    for (const category of mainCategories) {
      console.log(`\n${category.name} (slug: ${category.slug}):`);
      
      // Get subcategories
      const subcategories = await Category.findAll({
        where: { parentId: category.id },
        order: [['name', 'ASC']]
      });
      
      for (const subcat of subcategories) {
        console.log(`  - ${subcat.name} (slug: ${subcat.slug})`);
      }
    }
    
  } catch (error) {
    console.error('❌ Check failed:', error.message);
  } finally {
    process.exit(0);
  }
}

checkCategories();
