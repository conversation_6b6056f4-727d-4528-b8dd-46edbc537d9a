// Check subcategory slugs for women's t-shirts
require('dotenv').config();
const { connectDB } = require('./config/database');

async function checkSubcategory() {
  try {
    console.log('🔍 Checking subcategory slugs...');
    await connectDB();
    
    const { Category, Product } = require('./models');
    
    // Find women category
    const womenCategory = await Category.findOne({ where: { slug: 'women' } });
    console.log('Women category:', womenCategory ? womenCategory.toJSON() : 'Not found');
    
    if (womenCategory) {
      // Find all subcategories for women
      const subcategories = await Category.findAll({
        where: { parentId: womenCategory.id },
        order: [['name', 'ASC']]
      });
      
      console.log('\nWomen subcategories:');
      for (const subcat of subcategories) {
        const productCount = await Product.count({ where: { subcategoryId: subcat.id } });
        console.log(`- ${subcat.name} (slug: ${subcat.slug}) - ${productCount} products`);
      }
      
      // Try to find t-shirt subcategory specifically
      const tshirtSubcat = await Category.findOne({
        where: { 
          parentId: womenCategory.id,
          name: { [require('sequelize').Op.like]: '%T-shirt%' }
        }
      });
      
      if (tshirtSubcat) {
        console.log('\nFound T-shirt subcategory:', tshirtSubcat.toJSON());
        
        // Get some sample products
        const sampleProducts = await Product.findAll({
          where: { subcategoryId: tshirtSubcat.id },
          limit: 3
        });
        
        console.log('\nSample T-shirt products:');
        sampleProducts.forEach(product => {
          console.log(`- ${product.name} - $${product.salePrice}`);
        });
      }
    }
    
  } catch (error) {
    console.error('❌ Check failed:', error.message);
  } finally {
    process.exit(0);
  }
}

checkSubcategory();
