// Debug environment variables
require('dotenv').config();

console.log('🔍 Environment Variables Debug:');
console.log('NODE_ENV:', process.env.NODE_ENV);
console.log('PORT:', process.env.PORT);
console.log('DB_HOST:', process.env.DB_HOST);
console.log('DB_PORT:', process.env.DB_PORT);
console.log('DB_NAME:', process.env.DB_NAME);
console.log('DB_USER:', process.env.DB_USER);
console.log('DB_PASSWORD:', process.env.DB_PASSWORD ? '***' : 'undefined');
console.log('JWT_SECRET:', process.env.JWT_SECRET ? '***' : 'undefined');
console.log('CORS_ORIGIN:', process.env.CORS_ORIGIN);

// Test database connection
async function testDB() {
  try {
    console.log('\n🧪 Testing database connection...');
    const { connectDB } = require('./config/database');
    await connectDB();
    console.log('✅ Database connection successful!');
    
    const { Product } = require('./models');
    const count = await Product.count();
    console.log(`📦 Products in database: ${count}`);
    
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    console.error('Stack:', error.stack);
  }
}

testDB();
