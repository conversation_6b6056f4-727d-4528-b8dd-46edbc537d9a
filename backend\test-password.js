require('dotenv').config();
const { connectDB } = require('./config/database');
const { User } = require('./models');
const bcrypt = require('bcryptjs');

const testPassword = async () => {
  try {
    await connectDB();
    console.log('✅ Database connected');

    // Find the test user
    const user = await User.findOne({ where: { email: '<EMAIL>' } });
    if (!user) {
      console.log('❌ User not found');
      return;
    }

    console.log('✅ User found:', user.email);
    console.log('Stored password hash:', user.password);

    // Test password comparison
    const testPassword = 'password123';
    const isMatch = await bcrypt.compare(testPassword, user.password);
    console.log('Password match:', isMatch);

    // Test using the model method
    const isMatchModel = await user.comparePassword(testPassword);
    console.log('Model method match:', isMatchModel);

    process.exit(0);
  } catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
  }
};

testPassword();
