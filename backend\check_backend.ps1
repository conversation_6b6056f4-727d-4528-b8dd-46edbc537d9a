# Backend Connection Diagnostic Script
# Save as: check_backend.ps1

Write-Host "🔍 BACKEND CONNECTION DIAGNOSTIC" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Cyan

# Check if port 3001 is in use
Write-Host "`n1. Checking if port 3001 is in use..." -ForegroundColor Yellow
$port3001 = netstat -an | Select-String ":3001"
if ($port3001) {
    Write-Host "✅ Port 3001 is in use:" -ForegroundColor Green
    $port3001
} else {
    Write-Host "❌ Port 3001 is NOT in use - Backend is likely not running!" -ForegroundColor Red
}

# Check if backend process is running
Write-Host "`n2. Checking for Node.js processes..." -ForegroundColor Yellow
$nodeProcesses = Get-Process node -ErrorAction SilentlyContinue
if ($nodeProcesses) {
    Write-Host "✅ Node.js processes found:" -ForegroundColor Green
    $nodeProcesses | Format-Table -Property Id, ProcessName, CPU, WorkingSet
} else {
    Write-Host "❌ No Node.js processes running!" -ForegroundColor Red
}

# Test backend health endpoint
Write-Host "`n3. Testing backend health endpoint..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:3001/api/health" -TimeoutSec 5
    Write-Host "✅ Backend health check successful!" -ForegroundColor Green
    Write-Host "Response: $($response.StatusCode) - $($response.Content)" -ForegroundColor Green
} catch {
    Write-Host "❌ Backend health check failed!" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test CORS preflight
Write-Host "`n4. Testing CORS configuration..." -ForegroundColor Yellow
try {
    $headers = @{
        'Origin' = 'http://localhost:3000'
        'Access-Control-Request-Method' = 'POST'
        'Access-Control-Request-Headers' = 'Content-Type,Authorization'
    }
    $response = Invoke-WebRequest -Uri "http://localhost:3001/api/auth/login" -Method OPTIONS -Headers $headers -TimeoutSec 5
    Write-Host "✅ CORS preflight successful!" -ForegroundColor Green
} catch {
    Write-Host "❌ CORS preflight failed!" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n==================================" -ForegroundColor Cyan
Write-Host "💡 NEXT STEPS:" -ForegroundColor Cyan
Write-Host "- If port 3001 not in use: Start backend with 'npm start'" -ForegroundColor White
Write-Host "- If health check fails: Check backend logs for errors" -ForegroundColor White
Write-Host "- If CORS fails: Update CORS configuration" -ForegroundColor White
