// Comprehensive seed file with exact product counts as specified
require('dotenv').config();
const { connectDB, sequelize } = require('../config/database');
const { User, Category, Product } = require('../models');
const bcrypt = require('bcryptjs');

// Product data generators
const generateRandomPrice = (min = 15, max = 150) => {
  return parseFloat((Math.random() * (max - min) + min).toFixed(2));
};

const getRandomColors = () => {
  const allColors = ['black', 'white', 'red', 'blue', 'green', 'yellow', 'pink', 'purple', 'orange', 'brown', 'gray', 'navy', 'beige', 'maroon', 'teal'];
  const count = Math.floor(Math.random() * 4) + 1; // 1-4 colors
  const shuffled = allColors.sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
};

const getRandomSizes = (category) => {
  const clothingSizes = ['XS', 'S', 'M', 'L', 'XL', 'XXL'];
  const shoeSizes = ['6', '7', 'S', '9', '10', '11', '12'];
  const accessorySizes = ['One Size', 'S', 'M', 'L'];
  
  if (category.includes('Shoes')) return shoeSizes.slice(0, Math.floor(Math.random() * 4) + 3);
  if (category.includes('Accessories')) return accessorySizes.slice(0, Math.floor(Math.random() * 3) + 1);
  return clothingSizes.slice(0, Math.floor(Math.random() * 4) + 3);
};

const generateProductName = (subcategory, index) => {
  const prefixes = ['Classic', 'Modern', 'Vintage', 'Premium', 'Casual', 'Formal', 'Stylish', 'Comfortable', 'Trendy', 'Elegant'];
  const materials = ['Cotton', 'Denim', 'Silk', 'Wool', 'Polyester', 'Linen', 'Leather', 'Canvas'];
  const styles = ['Slim', 'Regular', 'Relaxed', 'Fitted', 'Loose', 'Cropped', 'Long', 'Short'];
  
  const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];
  const material = materials[Math.floor(Math.random() * materials.length)];
  const style = styles[Math.floor(Math.random() * styles.length)];
  
  return `${prefix} ${style} ${material} ${subcategory} ${index + 1}`;
};

const generateDescription = (name, subcategory) => {
  const descriptions = [
    `High-quality ${subcategory.toLowerCase()} perfect for everyday wear. Made with premium materials for comfort and durability.`,
    `Stylish ${subcategory.toLowerCase()} that combines fashion and functionality. Ideal for both casual and formal occasions.`,
    `Comfortable ${subcategory.toLowerCase()} designed with modern trends in mind. Features excellent craftsmanship and attention to detail.`,
    `Premium ${subcategory.toLowerCase()} offering superior quality and style. Perfect addition to your wardrobe collection.`,
    `Versatile ${subcategory.toLowerCase()} suitable for various occasions. Combines comfort with contemporary design elements.`
  ];
  return descriptions[Math.floor(Math.random() * descriptions.length)];
};

const generateImageName = (subcategory, gender, index) => {
  const genderPrefix = gender.toLowerCase();
  const subcatLower = subcategory.toLowerCase().replace(/[^a-z]/g, '');
  return `${genderPrefix}_${subcatLower}${(index % 50) + 1}.jpg`;
};

// Product count specifications
const productCounts = {
  'Women': {
    'T-shirt': 31,
    'Shirt': 43,
    'Jeans': 16,
    'Trouser': 17,
    'Jacket': 20,
    'Skirt': 17,
    'Shorts': 19,
    'Dress': 23,
    'Shoes': 20
  },
  'Men': {
    'T-shirt': 24,
    'Shirt': 22,
    'Jeans': 10,
    'Trouser': 9,
    'Jacket': 19,
    'Shoes': 17
  },
  'Boys': {
    'Clothing': 45,
    'Shoes': 17
  },
  'Girls': {
    'Clothing': 46,
    'Shoes': 20
  },
  'Accessories': {
    'Glasses': 19,
    'Watches': 6,
    'Gloves': 5,
    'Belt': 9,
    'Hat': 16,
    'Bag': 33,
    'Wallet': 8
  }
};

async function seedDatabase() {
  try {
    console.log('🌱 Starting comprehensive database seeding...');
    
    // Connect to database
    await connectDB();
    console.log('✅ Database connected');
    
    // Force sync to recreate tables
    await sequelize.sync({ force: true });
    console.log('✅ Database tables synchronized');
    
    // Create admin user
    const hashedPassword = await bcrypt.hash('admin123', 10);
    await User.create({
      name: 'Admin User',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'admin'
    });
    console.log('✅ Admin user created');
    
    // Create categories and subcategories
    const categories = {};
    const subcategories = {};
    
    for (const [categoryName, subcats] of Object.entries(productCounts)) {
      // Create main category
      const category = await Category.create({
        name: categoryName,
        description: `${categoryName} clothing and accessories`,
        slug: categoryName.toLowerCase(),
        isActive: true
      });
      categories[categoryName] = category;
      console.log(`✅ Created category: ${categoryName}`);
      
      // Create subcategories
      for (const subcatName of Object.keys(subcats)) {
        const subcategory = await Category.create({
          name: `${categoryName} ${subcatName}`,
          description: `${subcatName} for ${categoryName.toLowerCase()}`,
          parentId: category.id,
          slug: `${categoryName.toLowerCase()}-${subcatName.toLowerCase()}`,
          isActive: true
        });
        subcategories[`${categoryName}-${subcatName}`] = subcategory;
        console.log(`  ✅ Created subcategory: ${categoryName} ${subcatName}`);
      }
    }
    
    // Generate products
    let totalProducts = 0;
    
    for (const [categoryName, subcats] of Object.entries(productCounts)) {
      const category = categories[categoryName];
      
      for (const [subcatName, count] of Object.entries(subcats)) {
        const subcategory = subcategories[`${categoryName}-${subcatName}`];
        console.log(`\n📦 Generating ${count} products for ${categoryName} ${subcatName}...`);
        
        for (let i = 0; i < count; i++) {
          const originalPrice = generateRandomPrice(20, 200);
          const discount = Math.floor(Math.random() * 50); // 0-50% discount
          const salePrice = parseFloat((originalPrice * (1 - discount / 100)).toFixed(2));
          
          const product = await Product.create({
            name: generateProductName(subcatName, i),
            description: generateDescription(subcatName, subcatName),
            originalPrice,
            salePrice,
            discount,
            categoryId: category.id,
            subcategoryId: subcategory.id,
            images: JSON.stringify([generateImageName(subcatName, categoryName, i)]),
            colors: JSON.stringify(getRandomColors()),
            sizes: JSON.stringify(getRandomSizes(subcatName)),
            stock: Math.floor(Math.random() * 100) + 10, // 10-109 stock
            sku: `${categoryName.substring(0, 2).toUpperCase()}${subcatName.substring(0, 2).toUpperCase()}${String(i + 1).padStart(3, '0')}`,
            brand: 'StyleStore',
            material: ['Cotton', 'Polyester', 'Denim', 'Silk', 'Wool'][Math.floor(Math.random() * 5)],
            tags: JSON.stringify([subcatName.toLowerCase(), categoryName.toLowerCase(), 'fashion', 'clothing']),
            ratingAverage: parseFloat((Math.random() * 2 + 3).toFixed(1)), // 3.0-5.0 rating
            ratingCount: Math.floor(Math.random() * 100),
            isActive: true,
            isFeatured: Math.random() < 0.1, // 10% chance of being featured
            slug: `${generateProductName(subcatName, i).toLowerCase().replace(/\s+/g, '-')}-${i + 1}`
          });
          
          totalProducts++;
          
          if ((i + 1) % 10 === 0) {
            console.log(`  📦 Created ${i + 1}/${count} products for ${categoryName} ${subcatName}`);
          }
        }
        
        console.log(`✅ Completed ${count} products for ${categoryName} ${subcatName}`);
      }
    }
    
    console.log('\n🎉 Database seeding completed successfully!');
    console.log(`\n📊 Summary:`);
    console.log(`- Categories: ${Object.keys(categories).length}`);
    console.log(`- Subcategories: ${Object.keys(subcategories).length}`);
    console.log(`- Products: ${totalProducts}`);
    console.log(`- Users: 1 (admin)`);
    
    // Verify counts
    console.log('\n📋 Product count verification:');
    for (const [categoryName, subcats] of Object.entries(productCounts)) {
      console.log(`\n${categoryName}:`);
      for (const [subcatName, expectedCount] of Object.entries(subcats)) {
        const subcategory = subcategories[`${categoryName}-${subcatName}`];
        const actualCount = await Product.count({ where: { subcategoryId: subcategory.id } });
        console.log(`  ${subcatName}: ${actualCount}/${expectedCount} ${actualCount === expectedCount ? '✅' : '❌'}`);
      }
    }
    
    console.log('\n✅ Seeding completed successfully!');
    
  } catch (error) {
    console.error('❌ Seeding failed:', error);
    console.error(error.stack);
  } finally {
    process.exit(0);
  }
}

seedDatabase();
