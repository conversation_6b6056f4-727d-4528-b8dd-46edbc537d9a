require('dotenv').config();
const { connectDB } = require('./config/database');
const { User } = require('./models');
const bcrypt = require('bcryptjs');

const createNewUser = async () => {
  try {
    await connectDB();
    console.log('✅ Database connected');

    // Delete existing test user
    await User.destroy({ where: { email: '<EMAIL>' } });
    console.log('✅ Existing user deleted');

    // Create new user with correct password
    const hashedPassword = await bcrypt.hash('password123', 10);
    const testUser = await User.create({
      name: 'Test User',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'user',
      isActive: true
    });
    console.log('✅ New test user created');

    // Test the password
    const isMatch = await testUser.comparePassword('password123');
    console.log('Password test:', isMatch);

    process.exit(0);
  } catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
  }
};

createNewUser();
