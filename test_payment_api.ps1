# 💳 Payment API Testing Script
# File: test_payment_api.ps1

# ================================
# 🔧 CONFIGURATION
# ================================
$baseUrl = "http://localhost:3001/api"
$frontendUrl = "http://localhost:5174"

# Colors for output
$Green = "Green"
$Red = "Red"
$Yellow = "Yellow"
$Cyan = "Cyan"

Write-Host "💳 PAYMENT API TESTING SCRIPT" -ForegroundColor $Cyan
Write-Host "================================" -ForegroundColor $Cyan

# ================================
# 🧪 HELPER FUNCTIONS
# ================================

function Test-Backend {
    try {
        Write-Host "🔍 Testing backend connection..." -ForegroundColor $Yellow
        $response = Invoke-RestMethod -Uri "$baseUrl/health" -Method GET -TimeoutSec 10
        Write-Host "✅ Backend is running: $($response.message)" -ForegroundColor $Green
        return $true
    }
    catch {
        Write-Host "❌ Backend connection failed: $($_.Exception.Message)" -ForegroundColor $Red
        return $false
    }
}

function Get-AuthToken {
    param([string]$email, [string]$password)
    
    try {
        Write-Host "🔐 Logging in as $email..." -ForegroundColor $Yellow
        
        $loginData = @{
            email = $email
            password = $password
        } | ConvertTo-Json
        
        $response = Invoke-RestMethod -Uri "$baseUrl/auth/login" -Method POST -Body $loginData -ContentType "application/json"
        
        if ($response.token) {
            Write-Host "✅ Login successful" -ForegroundColor $Green
            return $response.token
        } else {
            Write-Host "❌ Login failed: No token received" -ForegroundColor $Red
            return $null
        }
    }
    catch {
        Write-Host "❌ Login failed: $($_.Exception.Message)" -ForegroundColor $Red
        return $null
    }
}

function Create-TestOrder {
    param([string]$token)
    
    try {
        Write-Host "📦 Creating test order..." -ForegroundColor $Yellow
        
        $headers = @{
            "Authorization" = "Bearer $token"
            "Content-Type" = "application/json"
        }
        
        $response = Invoke-RestMethod -Uri "$baseUrl/test/create-test-order" -Method POST -Headers $headers
        
        if ($response.success) {
            Write-Host "✅ Test order created successfully" -ForegroundColor $Green
            Write-Host "   Order ID: $($response.order.id)" -ForegroundColor $Cyan
            Write-Host "   Order Number: $($response.order.orderNumber)" -ForegroundColor $Cyan
            Write-Host "   Total: $$($response.order.total)" -ForegroundColor $Cyan
            return $response.order.id
        } else {
            Write-Host "❌ Failed to create test order: $($response.message)" -ForegroundColor $Red
            return $null
        }
    }
    catch {
        Write-Host "❌ Create order error: $($_.Exception.Message)" -ForegroundColor $Red
        return $null
    }
}

function Process-TestPayment {
    param([string]$token, [int]$orderId)
    
    try {
        Write-Host "💳 Processing payment for order $orderId..." -ForegroundColor $Yellow
        
        $headers = @{
            "Authorization" = "Bearer $token"
            "Content-Type" = "application/json"
        }
        
        $response = Invoke-RestMethod -Uri "$baseUrl/test/process-payment/$orderId" -Method POST -Headers $headers
        
        if ($response.success) {
            Write-Host "✅ Payment processed successfully" -ForegroundColor $Green
            Write-Host "   Transaction ID: $($response.data.payment.transactionId)" -ForegroundColor $Cyan
            Write-Host "   Amount: $$($response.data.payment.amount)" -ForegroundColor $Cyan
            Write-Host "   Status: $($response.data.payment.status)" -ForegroundColor $Cyan
            Write-Host "   Order Status: $($response.data.order.status)" -ForegroundColor $Cyan
            
            if ($response.data.stockUpdates) {
                Write-Host "📦 Stock Updates:" -ForegroundColor $Yellow
                foreach ($update in $response.data.stockUpdates) {
                    Write-Host "   Product: $($update.productName) | Sold: $($update.soldQuantity) | New Stock: $($update.newStock)" -ForegroundColor $Cyan
                }
            }
            
            return $response.data.payment
        } else {
            Write-Host "❌ Payment processing failed: $($response.message)" -ForegroundColor $Red
            return $null
        }
    }
    catch {
        Write-Host "❌ Payment processing error: $($_.Exception.Message)" -ForegroundColor $Red
        return $null
    }
}

function Get-PaymentSummary {
    param([string]$token)
    
    try {
        Write-Host "📊 Getting payment summary..." -ForegroundColor $Yellow
        
        $headers = @{
            "Authorization" = "Bearer $token"
        }
        
        $response = Invoke-RestMethod -Uri "$baseUrl/test/payment-summary" -Method GET -Headers $headers
        
        if ($response.success) {
            Write-Host "✅ Payment summary retrieved" -ForegroundColor $Green
            Write-Host "   Total Payments: $($response.count)" -ForegroundColor $Cyan
            
            if ($response.payments.Length -gt 0) {
                Write-Host "📋 Recent Payments:" -ForegroundColor $Yellow
                foreach ($payment in $response.payments[0..2]) {  # Show first 3
                    Write-Host "   ID: $($payment.id) | Amount: $$($payment.amount) | Status: $($payment.status)" -ForegroundColor $Cyan
                }
            }
            
            return $response.payments
        } else {
            Write-Host "❌ Failed to get payment summary: $($response.message)" -ForegroundColor $Red
            return $null
        }
    }
    catch {
        Write-Host "❌ Payment summary error: $($_.Exception.Message)" -ForegroundColor $Red
        return $null
    }
}

function Get-DatabaseStats {
    param([string]$token)
    
    try {
        Write-Host "📈 Getting database statistics..." -ForegroundColor $Yellow
        
        $headers = @{
            "Authorization" = "Bearer $token"
        }
        
        $response = Invoke-RestMethod -Uri "$baseUrl/test/db-stats" -Method GET -Headers $headers
        
        if ($response.success) {
            Write-Host "✅ Database statistics retrieved" -ForegroundColor $Green
            $stats = $response.stats
            Write-Host "📊 Database Overview:" -ForegroundColor $Yellow
            Write-Host "   Users: $($stats.users)" -ForegroundColor $Cyan
            Write-Host "   Products: $($stats.products)" -ForegroundColor $Cyan
            Write-Host "   Orders: $($stats.orders)" -ForegroundColor $Cyan
            Write-Host "   Payments: $($stats.payments)" -ForegroundColor $Cyan
            Write-Host "   Paid Orders: $($stats.paidOrders)" -ForegroundColor $Cyan
            Write-Host "   Total Revenue: $$($stats.totalRevenue)" -ForegroundColor $Green
            
            return $stats
        } else {
            Write-Host "❌ Failed to get database stats: $($response.message)" -ForegroundColor $Red
            return $null
        }
    }
    catch {
        Write-Host "❌ Database stats error: $($_.Exception.Message)" -ForegroundColor $Red
        return $null
    }
}

function Cleanup-TestData {
    param([string]$token)
    
    Write-Host "🧹 Do you want to cleanup test data? (y/n): " -ForegroundColor $Yellow -NoNewline
    $cleanup = Read-Host
    
    if ($cleanup -eq 'y' -or $cleanup -eq 'Y') {
        try {
            $headers = @{
                "Authorization" = "Bearer $token"
            }
            
            $response = Invoke-RestMethod -Uri "$baseUrl/test/cleanup-test-data" -Method DELETE -Headers $headers
            
            if ($response.success) {
                Write-Host "✅ Test data cleaned up successfully" -ForegroundColor $Green
            } else {
                Write-Host "❌ Cleanup failed: $($response.message)" -ForegroundColor $Red
            }
        }
        catch {
            Write-Host "❌ Cleanup error: $($_.Exception.Message)" -ForegroundColor $Red
        }
    }
}

# ================================
# 🚀 MAIN TESTING FLOW
# ================================

Write-Host ""
Write-Host "🚀 Starting Payment API Tests..." -ForegroundColor $Green
Write-Host ""

# Test 1: Backend Connection
if (-not (Test-Backend)) {
    Write-Host "❌ Cannot proceed without backend connection" -ForegroundColor $Red
    exit 1
}

Write-Host ""

# Test 2: Authentication
Write-Host "📝 Enter login credentials:" -ForegroundColor $Yellow
$email = Read-Host "Email"
$password = Read-Host "Password" -AsSecureString
$passwordText = [System.Runtime.InteropServices.Marshal]::PtrToStringAuto([System.Runtime.InteropServices.Marshal]::SecureStringToBSTR($password))

$token = Get-AuthToken -email $email -password $passwordText

if (-not $token) {
    Write-Host "❌ Cannot proceed without authentication" -ForegroundColor $Red
    exit 1
}

Write-Host ""

# Test 3: Database Stats (Before)
Write-Host "📊 Initial Database State:" -ForegroundColor $Yellow
$initialStats = Get-DatabaseStats -token $token

Write-Host ""

# Test 4: Create Test Order
$orderId = Create-TestOrder -token $token

if (-not $orderId) {
    Write-Host "❌ Cannot proceed without test order" -ForegroundColor $Red
    exit 1
}

Write-Host ""

# Test 5: Process Payment
$payment = Process-TestPayment -token $token -orderId $orderId

Write-Host ""

# Test 6: Payment Summary
$payments = Get-PaymentSummary -token $token

Write-Host ""

# Test 7: Database Stats (After)
Write-Host "📊 Updated Database State:" -ForegroundColor $Yellow
$finalStats = Get-DatabaseStats -token $token

Write-Host ""

# Test 8: Show Changes
if ($initialStats -and $finalStats) {
    Write-Host "📈 Changes Made:" -ForegroundColor $Green
    Write-Host "   Orders: $($initialStats.orders) → $($finalStats.orders) (+$($finalStats.orders - $initialStats.orders))" -ForegroundColor $Cyan
    Write-Host "   Payments: $($initialStats.payments) → $($finalStats.payments) (+$($finalStats.payments - $initialStats.payments))" -ForegroundColor $Cyan
    Write-Host "   Revenue: $$($initialStats.totalRevenue) → $$($finalStats.totalRevenue) (+$$([math]::Round($finalStats.totalRevenue - $initialStats.totalRevenue, 2)))" -ForegroundColor $Green
}

Write-Host ""

# Test 9: Cleanup
Cleanup-TestData -token $token

Write-Host ""
Write-Host "🎉 Payment API testing completed!" -ForegroundColor $Green
Write-Host ""
Write-Host "💡 Next Steps:" -ForegroundColor $Yellow
Write-Host "1. Check database with SQL commands from payment_database_commands.sql" -ForegroundColor $Cyan
Write-Host "2. Test frontend integration at $frontendUrl" -ForegroundColor $Cyan
Write-Host "3. Monitor server logs for any issues" -ForegroundColor $Cyan
