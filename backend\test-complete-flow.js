const axios = require('axios');

const testCompleteFlow = async () => {
  try {
    console.log('🚀 Testing complete e-commerce flow...\n');

    // 1. Register a new user
    console.log('1. Registering new user...');
    const email = `test_complete_${Date.now()}@example.com`;
    const registerResponse = await axios.post('http://localhost:3001/api/auth/register', {
      name: 'Complete Test User',
      email: email,
      password: 'password123'
    });

    const token = registerResponse.data.token;
    console.log('✅ User registered successfully');

    // 2. Get products
    console.log('\n2. Fetching products...');
    const productsResponse = await axios.get('http://localhost:3001/api/products?limit=1');
    const product = productsResponse.data.products[0];
    console.log(`✅ Product found: ${product.name} - $${product.salePrice}`);

    // 3. Make a purchase
    console.log('\n3. Making a purchase...');
    const paymentData = {
      items: [
        {
          productId: product.id,
          quantity: 2,
          color: 'black',
          size: 'M',
          price: parseFloat(product.salePrice)
        }
      ],
      shippingAddress: {
        firstName: 'Complete',
        lastName: 'Test',
        email: email,
        address: '123 Test Street',
        city: 'Test City',
        zipCode: '12345',
        country: 'USA',
        phone: '555-0123'
      },
      billingAddress: {
        firstName: 'Complete',
        lastName: 'Test',
        email: email,
        address: '123 Test Street',
        city: 'Test City',
        zipCode: '12345',
        country: 'USA',
        phone: '555-0123'
      },
      paymentMethod: 'credit_card',
      paymentGateway: 'manual',
      transactionId: `TXN-${Date.now()}-${Math.floor(Math.random() * 10000)}`
    };

    const paymentResponse = await axios.post('http://localhost:3001/api/payments/purchase', paymentData, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('✅ Purchase completed successfully');
    console.log(`   Order Number: ${paymentResponse.data.order.orderNumber}`);
    console.log(`   Total: $${paymentResponse.data.order.total}`);

    // 4. Check order history
    console.log('\n4. Checking order history...');
    const ordersResponse = await axios.get('http://localhost:3001/api/orders/my-orders', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    console.log('✅ Order history retrieved successfully');
    console.log(`   Total orders: ${ordersResponse.data.orders ? ordersResponse.data.orders.length : 0}`);
    if (ordersResponse.data.orders && ordersResponse.data.orders.length > 0) {
      const order = ordersResponse.data.orders[0];
      console.log(`   Latest order: ${order.orderNumber} - $${order.total} - ${order.orderStatus}`);
    }

    // 5. Get user profile
    console.log('\n5. Getting user profile...');
    const profileResponse = await axios.get('http://localhost:3001/api/user/profile', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    console.log('✅ User profile retrieved successfully');
    console.log(`   Name: ${profileResponse.data.user.name}`);
    console.log(`   Email: ${profileResponse.data.user.email}`);

    console.log('\n🎉 Complete e-commerce flow test PASSED! All features working correctly.');

  } catch (error) {
    console.error('\n❌ Test failed:', error.response?.data || error.message);
    if (error.response?.status === 400) {
      console.log('Full error response:', JSON.stringify(error.response.data, null, 2));
    }
  }
};

testCompleteFlow();
