// Test startup script
console.log('Starting test...');

try {
  require('dotenv').config();
  console.log('✅ dotenv loaded');
  
  const express = require('express');
  console.log('✅ express loaded');
  
  const { connectDB } = require('./config/database');
  console.log('✅ database config loaded');
  
  const app = express();
  console.log('✅ express app created');
  
  app.get('/test', (req, res) => {
    res.json({ message: 'Test endpoint working' });
  });
  
  const PORT = process.env.PORT || 3001;
  
  connectDB().then(() => {
    console.log('✅ Database connected');
    
    app.listen(PORT, () => {
      console.log(`✅ Server running on port ${PORT}`);
      console.log(`Test URL: http://localhost:${PORT}/test`);
    });
  }).catch(err => {
    console.error('❌ Database connection failed:', err.message);
  });
  
} catch (error) {
  console.error('❌ Startup error:', error.message);
  console.error(error.stack);
}
