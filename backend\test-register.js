const axios = require('axios');

const testRegister = async () => {
  try {
    // First delete existing user
    console.log('Deleting existing user...');
    
    // Register new user
    const response = await axios.post('http://localhost:3001/api/auth/register', {
      name: 'Test User',
      email: '<EMAIL>',
      password: 'password123'
    });

    const data = response.data;
    console.log('Registration response:', data);
    
    if (data.token) {
      console.log('✅ Registration successful, token:', data.token);
      
      // Now test login
      const loginResponse = await axios.post('http://localhost:3001/api/auth/login', {
        email: '<EMAIL>',
        password: 'password123'
      });
      
      console.log('Login response:', loginResponse.data);
      
      if (loginResponse.data.token) {
        console.log('✅ Login successful after registration');
        return loginResponse.data.token;
      }
    } else {
      console.log('❌ Registration failed');
    }
  } catch (error) {
    console.error('❌ Error:', error.response?.data || error.message);
  }
};

testRegister();
