// Quick test to check database connection
require('dotenv').config();
const { connectDB } = require('./config/database');

async function quickTest() {
  try {
    console.log('🧪 Testing database connection...');
    await connectDB();
    console.log('✅ Database connected successfully!');
    
    const { User, Category, Product } = require('./models');
    
    // Test basic operations
    const userCount = await User.count();
    const categoryCount = await Category.count();
    const productCount = await Product.count();
    
    console.log(`📊 Current database state:`);
    console.log(`- Users: ${userCount}`);
    console.log(`- Categories: ${categoryCount}`);
    console.log(`- Products: ${productCount}`);
    
    console.log('✅ Database test completed!');
    
  } catch (error) {
    console.error('❌ Database test failed:', error.message);
  } finally {
    process.exit(0);
  }
}

quickTest();
